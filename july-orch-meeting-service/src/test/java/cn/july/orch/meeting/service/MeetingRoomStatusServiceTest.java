package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.entity.MeetingRoom;
import cn.july.orch.meeting.enums.MeetingRoomStatusEnum;
import cn.july.orch.meeting.repository.MeetingRoomRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class MeetingRoomStatusServiceTest {

    @Mock
    private MeetingRoomRepository meetingRoomRepository;

    @InjectMocks
    private MeetingRoomStatusServiceImpl meetingRoomStatusService;

    @Test
    void testUpdateMeetingRoomStatus() {
        // 准备测试数据
        Long meetingRoomId = 1L;
        MeetingRoomStatusEnum status = MeetingRoomStatusEnum.RESERVED;
        MeetingRoom meetingRoom = new MeetingRoom();
        meetingRoom.setId(meetingRoomId);

        // 设置mock行为
        when(meetingRoomRepository.findById(meetingRoomId)).thenReturn(meetingRoom);

        // 执行测试
        meetingRoomStatusService.updateMeetingRoomStatus(meetingRoomId, status);

        // 验证结果
        assertEquals(status, meetingRoom.getStatus());
        verify(meetingRoomRepository).save(meetingRoom);
    }

    @Test
    void testIsMeetingRoomFree() {
        // 准备测试数据
        Long meetingRoomId = 1L;
        MeetingRoom meetingRoom = new MeetingRoom();
        meetingRoom.setId(meetingRoomId);
        meetingRoom.setStatus(MeetingRoomStatusEnum.FREE);

        // 设置mock行为
        when(meetingRoomRepository.findById(meetingRoomId)).thenReturn(meetingRoom);

        // 执行测试
        boolean result = meetingRoomStatusService.isMeetingRoomFree(meetingRoomId);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsMeetingRoomNotFree() {
        // 准备测试数据
        Long meetingRoomId = 1L;
        MeetingRoom meetingRoom = new MeetingRoom();
        meetingRoom.setId(meetingRoomId);
        meetingRoom.setStatus(MeetingRoomStatusEnum.RESERVED);

        // 设置mock行为
        when(meetingRoomRepository.findById(meetingRoomId)).thenReturn(meetingRoom);

        // 执行测试
        boolean result = meetingRoomStatusService.isMeetingRoomFree(meetingRoomId);

        // 验证结果
        assertFalse(result);
    }
}