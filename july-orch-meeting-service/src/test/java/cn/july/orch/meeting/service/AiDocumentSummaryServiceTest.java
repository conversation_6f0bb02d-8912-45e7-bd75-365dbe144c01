package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.dromara.x.file.storage.core.FileStorageService;
import org.dromara.x.file.storage.core.FileInfo;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.domain.response.AgentCompleteRespDTO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> Assistant
 * @description AI文档汇总服务测试
 */
@ExtendWith(MockitoExtension.class)
class AiDocumentSummaryServiceTest {

    @InjectMocks
    private AiDocumentSummaryService aiDocumentSummaryService;

    @Mock
    private AiEmpowermentService aiEmpowermentService;
    
    @Mock
    private FileStorageService fileStorageService;
    
    @Mock
    private FileDetailService fileDetailService;

    private List<PreMeetingDocumentDTO> testDocuments;

    @BeforeEach
    void setUp() {
        testDocuments = Arrays.asList(
            PreMeetingDocumentDTO.builder()
                .fileKey("file1-key")
                .fileName("会议议程.pdf")
                .build(),
            PreMeetingDocumentDTO.builder()
                .fileKey("file2-key")
                .fileName("项目方案.doc")
                .build()
        );
    }

    @Test
    void testSummarizeDocuments_EnabledWithDocuments() {
        // Given
        Boolean enableDocAiSummary = true;

        // When
        String result = aiDocumentSummaryService.summarizeDocuments(testDocuments, enableDocAiSummary);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.contains("会前文档AI汇总"));
        assertTrue(result.contains("会议议程.pdf"));
        assertTrue(result.contains("项目方案.doc"));
        assertTrue(result.contains("分割线"));
        
        // 验证文档状态已更新
        for (PreMeetingDocumentDTO doc : testDocuments) {
            assertEquals("SUCCESS", doc.getAiSummaryStatus());
            assertNotNull(doc.getAiSummaryContent());
            assertFalse(doc.getAiSummaryContent().isEmpty());
        }
    }

    @Test
    void testSummarizeDocuments_Disabled() {
        // Given
        Boolean enableDocAiSummary = false;

        // When
        String result = aiDocumentSummaryService.summarizeDocuments(testDocuments, enableDocAiSummary);

        // Then
        assertEquals("", result);
    }

    @Test
    void testSummarizeDocuments_NullFlag() {
        // Given
        Boolean enableDocAiSummary = null;

        // When
        String result = aiDocumentSummaryService.summarizeDocuments(testDocuments, enableDocAiSummary);

        // Then
        assertEquals("", result);
    }

    @Test
    void testSummarizeDocuments_EmptyDocuments() {
        // Given
        Boolean enableDocAiSummary = true;
        List<PreMeetingDocumentDTO> emptyDocuments = new ArrayList<>();

        // When
        String result = aiDocumentSummaryService.summarizeDocuments(emptyDocuments, enableDocAiSummary);

        // Then
        assertEquals("", result);
    }

    @Test
    void testSummarizeDocuments_NullDocuments() {
        // Given
        Boolean enableDocAiSummary = true;

        // When
        String result = aiDocumentSummaryService.summarizeDocuments(null, enableDocAiSummary);

        // Then
        assertEquals("", result);
    }

    @Test
    void testInitializeDocumentSummaryStatus() {
        // Given
        List<PreMeetingDocumentDTO> documents = Arrays.asList(
            PreMeetingDocumentDTO.builder()
                .fileKey("file1.pdf")
                .fileName("测试文档1.pdf")
                .build(),
            PreMeetingDocumentDTO.builder()
                .fileKey("file2.doc")
                .fileName("测试文档2.doc")
                .aiSummaryStatus("EXISTING")
                .aiSummaryContent("existing content")
                .build()
        );

        // When
        aiDocumentSummaryService.initializeDocumentSummaryStatus(documents);

        // Then
        assertEquals("PENDING", documents.get(0).getAiSummaryStatus());
        assertEquals("", documents.get(0).getAiSummaryContent());
        
        // 已有状态的文档不应被覆盖
        assertEquals("EXISTING", documents.get(1).getAiSummaryStatus());
        assertEquals("existing content", documents.get(1).getAiSummaryContent());
    }

    @Test
    void testInitializeDocumentSummaryStatus_EmptyList() {
        // Given
        List<PreMeetingDocumentDTO> emptyDocuments = new ArrayList<>();

        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> 
            aiDocumentSummaryService.initializeDocumentSummaryStatus(emptyDocuments)
        );
    }

    @Test
    void testInitializeDocumentSummaryStatus_NullList() {
        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> 
            aiDocumentSummaryService.initializeDocumentSummaryStatus(null)
        );
    }

    @Test
    void testSummaryContentFormat() {
        // Given
        Boolean enableDocAiSummary = true;
        List<PreMeetingDocumentDTO> singleDocument = Arrays.asList(
            PreMeetingDocumentDTO.builder()
                .fileKey("single.pdf")
                .fileName("单个文档.pdf")
                .build()
        );

        // When
        String result = aiDocumentSummaryService.summarizeDocuments(singleDocument, enableDocAiSummary);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("【会前文档AI汇总】"));
        assertTrue(result.contains("【文档1】单个文档.pdf"));
        assertTrue(result.contains("汇总内容："));
    }

    @Test
    void testSummarizeDocumentsAsync() throws ExecutionException, InterruptedException {
        // Given
        Boolean enableDocAiSummary = true;
        
        // 模拟文件存储服务和 AI 服务
        AgentCompleteRespDTO agentResponse = new AgentCompleteRespDTO();
        agentResponse.setAnswer("这是模拟AI汇总结果");
        
        // 模拟 AI 汇总服务返回成功结果
        when(aiEmpowermentService.callAiSummaryNonStream(any(), anyString())).thenReturn(agentResponse);

        // When
        CompletableFuture<String> result = aiDocumentSummaryService.summarizeDocumentsAsync(testDocuments, enableDocAiSummary);
        String summary = result.get();

        // Then
        assertNotNull(summary);
        assertFalse(summary.isEmpty());
        assertTrue(summary.contains("会前文档AI汇总"));
    }
}