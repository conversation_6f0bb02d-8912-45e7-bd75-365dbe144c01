package cn.july.orch.meeting.domain.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> Assistant
 * @description SimpleMeetingTagDTO 测试
 */
class SimpleMeetingTagDTOTest {

    @Test
    void testBuilder() {
        // Given
        Long id = 1L;
        String name = "重要会议";
        String color = "#FF0000";

        // When
        SimpleMeetingTagDTO tag = SimpleMeetingTagDTO.builder()
            .id(id)
            .name(name)
            .color(color)
            .build();

        // Then
        assertNotNull(tag);
        assertEquals(id, tag.getId());
        assertEquals(name, tag.getName());
        assertEquals(color, tag.getColor());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        SimpleMeetingTagDTO tag = new SimpleMeetingTagDTO();

        // When
        tag.setId(2L);
        tag.setName("例行会议");
        tag.setColor("#00FF00");

        // Then
        assertEquals(2L, tag.getId());
        assertEquals("例行会议", tag.getName());
        assertEquals("#00FF00", tag.getColor());
    }

    @Test
    void testEquals() {
        // Given
        SimpleMeetingTagDTO tag1 = SimpleMeetingTagDTO.builder()
            .id(1L)
            .name("测试标签")
            .color("#3498DB")
            .build();

        SimpleMeetingTagDTO tag2 = SimpleMeetingTagDTO.builder()
            .id(1L)
            .name("测试标签")
            .color("#3498DB")
            .build();

        SimpleMeetingTagDTO tag3 = SimpleMeetingTagDTO.builder()
            .id(2L)
            .name("不同标签")
            .color("#E74C3C")
            .build();

        // Then
        assertEquals(tag1, tag2);
        assertNotEquals(tag1, tag3);
    }
}