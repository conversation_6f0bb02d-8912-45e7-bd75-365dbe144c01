package cn.july.orch.meeting;

import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.enums.RecurrenceTypeEnum;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 重复性规则优化测试
 */
public class RecurrenceRuleOptimizationTest {

    @Test
    public void testYearlyRecurrence() {
        // 测试年重复功能
        NewMeeting meeting = new NewMeeting();
        meeting.setRecurrenceType(RecurrenceTypeEnum.YEARLY);
        meeting.setRecurrenceInterval(1);
        meeting.setStartTime(LocalDateTime.of(2024, 1, 15, 10, 0));
        meeting.setEndTime(LocalDateTime.of(2024, 1, 15, 11, 0));

        // 计算下次会议时间
        LocalDateTime nextTime = meeting.calculateNextMeetingTime();
        System.out.println("年重复 - 下次会议时间: " + nextTime);
        // 预期: 2025-01-15 10:00
    }

    @Test
    public void testYearlyRecurrenceWithInterval() {
        // 测试年重复功能 - 每2年
        NewMeeting meeting = new NewMeeting();
        meeting.setRecurrenceType(RecurrenceTypeEnum.YEARLY);
        meeting.setRecurrenceInterval(2);
        meeting.setStartTime(LocalDateTime.of(2024, 1, 15, 10, 0));
        meeting.setEndTime(LocalDateTime.of(2024, 1, 15, 11, 0));

        // 计算下次会议时间
        LocalDateTime nextTime = meeting.calculateNextMeetingTime();
        System.out.println("年重复(每2年) - 下次会议时间: " + nextTime);
        // 预期: 2026-01-15 10:00
    }

    @Test
    public void testYearlyRecurrenceLeapYear() {
        // 测试年重复功能 - 2月29日
        NewMeeting meeting = new NewMeeting();
        meeting.setRecurrenceType(RecurrenceTypeEnum.YEARLY);
        meeting.setRecurrenceInterval(1);
        meeting.setStartTime(LocalDateTime.of(2024, 2, 29, 10, 0)); // 2024是闰年
        meeting.setEndTime(LocalDateTime.of(2024, 2, 29, 11, 0));

        // 计算下次会议时间
        LocalDateTime nextTime = meeting.calculateNextMeetingTime();
        System.out.println("年重复(2月29日) - 下次会议时间: " + nextTime);
        // 预期: 2025-02-28 10:00 (2025不是闰年)
    }

    @Test
    public void testMonthlyMultiSelectRecurrence() {
        // 测试月重复多选功能
        NewMeeting meeting = new NewMeeting();
        meeting.setRecurrenceType(RecurrenceTypeEnum.MONTHLY);
        meeting.setRecurrenceInterval(1);
        meeting.setRecurrenceMonthDays("1,15,30"); // 每月1号、15号、30号
        meeting.setStartTime(LocalDateTime.of(2024, 1, 10, 10, 0));
        meeting.setEndTime(LocalDateTime.of(2024, 1, 10, 11, 0));

        // 计算下次会议时间
        LocalDateTime nextTime = meeting.calculateNextMeetingTime();
        System.out.println("月重复多选 - 下次会议时间: " + nextTime);
        // 预期: 2024-01-15 10:00 (当月15号)
    }

    @Test
    public void testMonthlyMultiSelectRecurrenceNextMonth() {
        // 测试月重复多选功能 - 跨月
        NewMeeting meeting = new NewMeeting();
        meeting.setRecurrenceType(RecurrenceTypeEnum.MONTHLY);
        meeting.setRecurrenceInterval(1);
        meeting.setRecurrenceMonthDays("1,15,30"); // 每月1号、15号、30号
        meeting.setStartTime(LocalDateTime.of(2024, 1, 25, 10, 0));
        meeting.setEndTime(LocalDateTime.of(2024, 1, 25, 11, 0));

        // 计算下次会议时间
        LocalDateTime nextTime = meeting.calculateNextMeetingTime();
        System.out.println("月重复多选(跨月) - 下次会议时间: " + nextTime);
        // 预期: 2024-02-01 10:00 (下月1号)
    }

    @Test
    public void testMonthlyMultiSelectRecurrenceFebruary() {
        // 测试月重复多选功能 - 2月30日不存在
        NewMeeting meeting = new NewMeeting();
        meeting.setRecurrenceType(RecurrenceTypeEnum.MONTHLY);
        meeting.setRecurrenceInterval(1);
        meeting.setRecurrenceMonthDays("1,15,30"); // 每月1号、15号、30号
        meeting.setStartTime(LocalDateTime.of(2024, 1, 30, 10, 0));
        meeting.setEndTime(LocalDateTime.of(2024, 1, 30, 11, 0));

        // 计算下次会议时间
        LocalDateTime nextTime = meeting.calculateNextMeetingTime();
        System.out.println("月重复多选(2月30日) - 下次会议时间: " + nextTime);
        // 预期: 2024-02-01 10:00 (2月没有30号，使用1号)
    }

    @Test
    public void testRecurrenceRuleDTO() {
        // 测试RecurrenceRule DTO的新字段
        List<Integer> monthDays = Arrays.asList(1, 15, 30);
        
        System.out.println("月重复日期列表: " + monthDays);
        System.out.println("转换为字符串: " + monthDays.stream()
                .map(String::valueOf)
                .reduce((a, b) -> a + "," + b)
                .orElse(""));
    }
}
