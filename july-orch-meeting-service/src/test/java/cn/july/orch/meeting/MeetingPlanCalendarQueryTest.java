package cn.july.orch.meeting;

import cn.july.orch.meeting.domain.dto.MeetingPlanCalendarDTO;
import cn.july.orch.meeting.domain.query.MeetingPlanCalendarQuery;
import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.RecurrenceTypeEnum;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 会议规划日历查询测试
 */
public class MeetingPlanCalendarQueryTest {

    @Test
    public void testCalendarQueryWithRecurringMeetings() {
        // 测试日历查询功能，包括重复性会议
        
        // 创建查询条件：查询2024年1月
        MeetingPlanCalendarQuery query = new MeetingPlanCalendarQuery();
        query.setStartDate(LocalDate.of(2024, 1, 1));
        query.setEndDate(LocalDate.of(2024, 1, 31));
        
        System.out.println("查询时间范围: " + query.getStartDate() + " 到 " + query.getEndDate());
        
        // 模拟一次性会议
        MeetingPlanCalendarDTO oneTimeMeeting = createOneTimeMeeting();
        System.out.println("一次性会议: " + oneTimeMeeting.getPlanName() + 
                          " 时间: " + oneTimeMeeting.getPlannedStartTime());
        
        // 模拟日重复会议
        MeetingPlanCalendarDTO dailyRecurringMeeting = createDailyRecurringMeeting();
        System.out.println("日重复会议: " + dailyRecurringMeeting.getPlanName() + 
                          " 原始时间: " + dailyRecurringMeeting.getPlannedStartTime() +
                          " 重复类型: " + dailyRecurringMeeting.getRecurrenceType());
        
        // 模拟周重复会议
        MeetingPlanCalendarDTO weeklyRecurringMeeting = createWeeklyRecurringMeeting();
        System.out.println("周重复会议: " + weeklyRecurringMeeting.getPlanName() + 
                          " 原始时间: " + weeklyRecurringMeeting.getPlannedStartTime() +
                          " 重复类型: " + weeklyRecurringMeeting.getRecurrenceType() +
                          " 星期几: " + weeklyRecurringMeeting.getRecurrenceWeekdays());
        
        // 模拟月重复会议
        MeetingPlanCalendarDTO monthlyRecurringMeeting = createMonthlyRecurringMeeting();
        System.out.println("月重复会议: " + monthlyRecurringMeeting.getPlanName() + 
                          " 原始时间: " + monthlyRecurringMeeting.getPlannedStartTime() +
                          " 重复类型: " + monthlyRecurringMeeting.getRecurrenceType() +
                          " 月日期: " + monthlyRecurringMeeting.getRecurrenceMonthDays());
        
        // 模拟年重复会议
        MeetingPlanCalendarDTO yearlyRecurringMeeting = createYearlyRecurringMeeting();
        System.out.println("年重复会议: " + yearlyRecurringMeeting.getPlanName() + 
                          " 原始时间: " + yearlyRecurringMeeting.getPlannedStartTime() +
                          " 重复类型: " + yearlyRecurringMeeting.getRecurrenceType());
        
        System.out.println("测试完成：日历查询功能支持重复性会议");
    }

    private MeetingPlanCalendarDTO createOneTimeMeeting() {
        MeetingPlanCalendarDTO meeting = new MeetingPlanCalendarDTO();
        meeting.setId(1L);
        meeting.setPlanName("一次性会议");
        meeting.setPlannedStartTime(LocalDateTime.of(2024, 1, 15, 10, 0));
        meeting.setPlannedEndTime(LocalDateTime.of(2024, 1, 15, 11, 0));
        meeting.setIsRecurring(IsRecurringEnum.NO);
        meeting.setIsRecurringInstance(false);
        return meeting;
    }

    private MeetingPlanCalendarDTO createDailyRecurringMeeting() {
        MeetingPlanCalendarDTO meeting = new MeetingPlanCalendarDTO();
        meeting.setId(2L);
        meeting.setPlanName("每日站会");
        meeting.setPlannedStartTime(LocalDateTime.of(2024, 1, 1, 9, 0));
        meeting.setPlannedEndTime(LocalDateTime.of(2024, 1, 1, 9, 30));
        meeting.setIsRecurring(IsRecurringEnum.YES);
        meeting.setRecurrenceType(RecurrenceTypeEnum.DAILY);
        meeting.setRecurrenceInterval(1);
        meeting.setRecurrenceEndDate(LocalDate.of(2024, 1, 31));
        meeting.setIsRecurringInstance(false);
        return meeting;
    }

    private MeetingPlanCalendarDTO createWeeklyRecurringMeeting() {
        MeetingPlanCalendarDTO meeting = new MeetingPlanCalendarDTO();
        meeting.setId(3L);
        meeting.setPlanName("周例会");
        meeting.setPlannedStartTime(LocalDateTime.of(2024, 1, 1, 14, 0));
        meeting.setPlannedEndTime(LocalDateTime.of(2024, 1, 1, 15, 0));
        meeting.setIsRecurring(IsRecurringEnum.YES);
        meeting.setRecurrenceType(RecurrenceTypeEnum.WEEKLY);
        meeting.setRecurrenceInterval(1);
        meeting.setRecurrenceWeekdays("2,4"); // 周一和周三
        meeting.setIsRecurringInstance(false);
        return meeting;
    }

    private MeetingPlanCalendarDTO createMonthlyRecurringMeeting() {
        MeetingPlanCalendarDTO meeting = new MeetingPlanCalendarDTO();
        meeting.setId(4L);
        meeting.setPlanName("月度总结会");
        meeting.setPlannedStartTime(LocalDateTime.of(2023, 12, 1, 10, 0));
        meeting.setPlannedEndTime(LocalDateTime.of(2023, 12, 1, 12, 0));
        meeting.setIsRecurring(IsRecurringEnum.YES);
        meeting.setRecurrenceType(RecurrenceTypeEnum.MONTHLY);
        meeting.setRecurrenceInterval(1);
        meeting.setRecurrenceMonthDays("1,15"); // 每月1号和15号
        meeting.setIsRecurringInstance(false);
        return meeting;
    }

    private MeetingPlanCalendarDTO createYearlyRecurringMeeting() {
        MeetingPlanCalendarDTO meeting = new MeetingPlanCalendarDTO();
        meeting.setId(5L);
        meeting.setPlanName("年度总结会");
        meeting.setPlannedStartTime(LocalDateTime.of(2023, 12, 31, 14, 0));
        meeting.setPlannedEndTime(LocalDateTime.of(2023, 12, 31, 17, 0));
        meeting.setIsRecurring(IsRecurringEnum.YES);
        meeting.setRecurrenceType(RecurrenceTypeEnum.YEARLY);
        meeting.setRecurrenceInterval(1);
        meeting.setIsRecurringInstance(false);
        return meeting;
    }

    @Test
    public void testRecurringInstanceGeneration() {
        // 测试重复实例生成逻辑
        
        MeetingPlanCalendarDTO originalMeeting = createDailyRecurringMeeting();
        System.out.println("原始会议: " + originalMeeting.getPlanName() + 
                          " 时间: " + originalMeeting.getPlannedStartTime());
        
        // 模拟生成重复实例
        LocalDateTime currentTime = originalMeeting.getPlannedStartTime();
        int count = 0;
        
        while (count < 5 && currentTime.isBefore(LocalDateTime.of(2024, 1, 6))) {
            MeetingPlanCalendarDTO instance = createRecurringInstance(originalMeeting, currentTime);
            System.out.println("重复实例 " + (count + 1) + ": " + instance.getPlanName() + 
                              " 时间: " + instance.getPlannedStartTime() +
                              " 是否重复实例: " + instance.getIsRecurringInstance() +
                              " 原始ID: " + instance.getOriginalPlanId());
            
            currentTime = currentTime.plusDays(1);
            count++;
        }
        
        System.out.println("重复实例生成测试完成");
    }

    private MeetingPlanCalendarDTO createRecurringInstance(MeetingPlanCalendarDTO originalPlan, LocalDateTime instanceTime) {
        MeetingPlanCalendarDTO instance = new MeetingPlanCalendarDTO();
        
        // 复制原始规划的所有属性
        instance.setId(originalPlan.getId());
        instance.setPlanName(originalPlan.getPlanName());
        instance.setPlannedStartTime(instanceTime);
        
        // 计算结束时间（保持相同的持续时长）
        if (originalPlan.getPlannedEndTime() != null && originalPlan.getPlannedStartTime() != null) {
            long durationMinutes = java.time.Duration.between(originalPlan.getPlannedStartTime(), originalPlan.getPlannedEndTime()).toMinutes();
            instance.setPlannedEndTime(instanceTime.plusMinutes(durationMinutes));
        }
        
        instance.setPlannedDuration(originalPlan.getPlannedDuration());
        instance.setStatus(originalPlan.getStatus());
        instance.setMeetingStandardName(originalPlan.getMeetingStandardName());
        instance.setPriorityLevel(originalPlan.getPriorityLevel());
        instance.setDepartmentName(originalPlan.getDepartmentName());
        instance.setMeetingLocation(originalPlan.getMeetingLocation());
        instance.setAttendeeCount(originalPlan.getAttendeeCount());
        instance.setCreateUserName(originalPlan.getCreateUserName());
        instance.setBackgroundColor(originalPlan.getBackgroundColor());
        
        // 设置重复性相关属性
        instance.setIsRecurring(originalPlan.getIsRecurring());
        instance.setRecurrenceType(originalPlan.getRecurrenceType());
        instance.setRecurrenceInterval(originalPlan.getRecurrenceInterval());
        instance.setRecurrenceWeekdays(originalPlan.getRecurrenceWeekdays());
        instance.setRecurrenceMonthDays(originalPlan.getRecurrenceMonthDays());
        instance.setRecurrenceEndDate(originalPlan.getRecurrenceEndDate());
        
        // 标记为重复实例
        instance.setIsRecurringInstance(true);
        instance.setOriginalPlanId(originalPlan.getId());
        
        return instance;
    }
}
