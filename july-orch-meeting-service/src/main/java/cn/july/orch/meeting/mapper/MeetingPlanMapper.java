package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.dto.MeetingPlanCalendarDTO;
import cn.july.orch.meeting.domain.dto.MeetingPlanListDTO;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import cn.july.orch.meeting.domain.query.MeetingPlanCalendarQuery;
import cn.july.orch.meeting.domain.query.MeetingPlanQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划Mapper
 * @date 2025-01-24
 */
@Mapper
public interface MeetingPlanMapper extends BaseMapper<MeetingPlanPO> {

    /**
     * 分页查询会议规划列表
     */
    Page<MeetingPlanListDTO> queryPage(Page<MeetingPlanPO> page, @Param("query") MeetingPlanQuery query);

    /**
     * 查询日历维度的会议规划
     */
    List<MeetingPlanCalendarDTO> queryCalendar(@Param("query") MeetingPlanCalendarQuery query);

    /**
     * 查询重复性会议规划
     */
    List<MeetingPlanCalendarDTO> queryRecurringPlans(@Param("query") MeetingPlanCalendarQuery query);
}
