package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.domain.response.AgentCompleteRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.StringJoiner;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Assistant
 * @description AI文档汇总服务
 */
@Slf4j
@Service
public class AiDocumentSummaryService {

    @Resource
    private AiEmpowermentService aiEmpowermentService;
    
    @Resource
    private FileStorageService fileStorageService;
    
    @Resource
    private FileDetailService fileDetailService;

    /**
     * 对会前文档进行AI汇总，并返回格式化的汇总内容（异步处理）
     * 
     * @param preMeetingDocuments 会前文档列表
     * @param enableDocAiSummary 是否启用文档AI汇总
     * @return 异步结果，包含格式化的汇总内容
     */
    @Async
    public CompletableFuture<String> summarizeDocumentsAsync(List<PreMeetingDocumentDTO> preMeetingDocuments, Boolean enableDocAiSummary) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return summarizeDocuments(preMeetingDocuments, enableDocAiSummary);
            } catch (Exception e) {
                log.error("异步文档AI汇总失败", e);
                return "";
            }
        });
    }

    /**
     * 对会前文档进行AI汇总，并返回格式化的汇总内容
     * 
     * @param preMeetingDocuments 会前文档列表
     * @param enableDocAiSummary 是否启用文档AI汇总
     * @return 格式化的汇总内容，如果未启用或无文档则返回空字符串
     */
    public String summarizeDocuments(List<PreMeetingDocumentDTO> preMeetingDocuments, Boolean enableDocAiSummary) {
        // 检查是否启用AI汇总
        if (enableDocAiSummary == null || !enableDocAiSummary) {
            log.debug("文档AI汇总功能未启用");
            return "";
        }

        // 检查是否有文档需要汇总
        if (CollectionUtils.isEmpty(preMeetingDocuments)) {
            log.debug("无会前文档需要汇总");
            return "";
        }

        log.info("开始对{}个会前文档进行AI汇总", preMeetingDocuments.size());

        StringJoiner summaryJoiner = new StringJoiner("\n\n================分割线================\n\n");
        summaryJoiner.add("【会前文档AI汇总】");

        for (int i = 0; i < preMeetingDocuments.size(); i++) {
            PreMeetingDocumentDTO document = preMeetingDocuments.get(i);
            String documentSummary = processDocumentSummary(document, i + 1);
            summaryJoiner.add(documentSummary);
        }

        String finalSummary = summaryJoiner.toString();
        log.info("文档AI汇总完成，总长度：{}", finalSummary.length());
        return finalSummary;
    }

    /**
     * 处理单个文档的AI汇总
     * 
     * @param document 文档信息
     * @param index 文档序号
     * @return 单个文档的汇总内容
     */
    private String processDocumentSummary(PreMeetingDocumentDTO document, int index) {
        StringJoiner documentSummary = new StringJoiner("\n");
        documentSummary.add(String.format("【文档%d】%s", index, document.getFileName()));

        try {
            // 从 filekey 获取文件并调用AI汇总
            String aiSummary = callAiSummaryWithFileKey(document.getFileKey(), document.getFileName());
            
            // 更新文档的AI汇总状态和内容
            document.setAiSummaryStatus("SUCCESS");
            document.setAiSummaryContent(aiSummary);
            
            documentSummary.add("汇总内容：");
            documentSummary.add(aiSummary);
            
            log.info("文档 {} AI汇总成功", document.getFileName());
            
        } catch (Exception e) {
            log.error("文档 {} AI汇总失败", document.getFileName(), e);
            
            // 更新文档的AI汇总状态为失败
            document.setAiSummaryStatus("FAILED");
            document.setAiSummaryContent("AI汇总失败：" + e.getMessage());
            
            documentSummary.add("汇总状态：失败");
            documentSummary.add("失败原因：" + e.getMessage());
        }

        return documentSummary.toString();
    }

    /**
     * 通过filekey调用AI智能体进行文档汇总
     * 
     * @param fileKey 文件关键字
     * @param fileName 文件名称
     * @return AI汇总结果
     */
    private String callAiSummaryWithFileKey(String fileKey, String fileName) {
        try {
            // 从 filekey 获取文件信息
            FileDetailPO fileDetailPO = fileDetailService.getById(fileKey);
            if (fileDetailPO == null) {
                throw new RuntimeException("文件不存在：" + fileKey);
            }

            // 获取文件URL
            FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
            if (fileInfo == null) {
                throw new RuntimeException("无法获取文件信息：" + fileKey);
            }

            // 下载文件到内存
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            fileStorageService.download(fileInfo).outputStream(outputStream);
            byte[] fileBytes = outputStream.toByteArray();

            // 创建 MultipartFile 对象
            MultipartFile multipartFile = createMultipartFileFromBytes(fileBytes, fileName, fileDetailPO.getContentType());

            // 调用现有的AiEmpowermentService进行文档汇总
            log.info("开始调用AI智能体汇总文档：{}", fileName);
            
            // 使用默认的汇总提示词
            String customPrompt = "请对这个文档进行详细的汇总，重点关注关键信息和要点。";
            
            // 调用现有的智能体服务（非流式）
            AgentCompleteRespDTO response = aiEmpowermentService.callAiSummaryNonStream(multipartFile, customPrompt);
            
            if (response != null && response.getAnswer() != null) {
                return response.getAnswer();
            } else {
                throw new RuntimeException("智能体返回结果为空");
            }
            
        } catch (Exception e) {
            log.error("调用AI智能体汇总文档失败，文件：{}", fileName, e);
            throw new RuntimeException("智能体汇总失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从字节数组创建 MultipartFile 对象
     */
    private MultipartFile createMultipartFileFromBytes(byte[] fileBytes, String fileName, String contentType) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return "file";
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return contentType;
            }

            @Override
            public boolean isEmpty() {
                return fileBytes.length == 0;
            }

            @Override
            public long getSize() {
                return fileBytes.length;
            }

            @Override
            public byte[] getBytes() {
                return fileBytes;
            }

            @Override
            public java.io.InputStream getInputStream() {
                return new ByteArrayInputStream(fileBytes);
            }

            @Override
            public void transferTo(java.io.File dest) throws IOException {
                throw new UnsupportedOperationException("不支持此操作");
            }
        };
    }

    /**
     * 检查并初始化文档的AI汇总状态
     * 
     * @param preMeetingDocuments 会前文档列表
     */
    public void initializeDocumentSummaryStatus(List<PreMeetingDocumentDTO> preMeetingDocuments) {
        if (CollectionUtils.isEmpty(preMeetingDocuments)) {
            return;
        }

        for (PreMeetingDocumentDTO document : preMeetingDocuments) {
            if (document.getAiSummaryStatus() == null) {
                document.setAiSummaryStatus("PENDING");
            }
            if (document.getAiSummaryContent() == null) {
                document.setAiSummaryContent("");
            }
        }
    }
}