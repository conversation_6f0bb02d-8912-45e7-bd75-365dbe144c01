package cn.july.orch.meeting.repository;

import cn.july.orch.meeting.domain.entity.TaskListAgg;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务清单仓储接口
 */
public interface ITaskListRepository {

    /**
     * 插入任务清单
     *
     * @param taskListAgg 任务清单聚合
     * @return 任务清单ID
     */
    Long insert(TaskListAgg taskListAgg);

    /**
     * 更新任务清单
     *
     * @param taskListAgg 任务清单聚合
     */
    void update(TaskListAgg taskListAgg);

    /**
     * 根据ID查询任务清单
     *
     * @param id 任务清单ID
     * @return 任务清单聚合
     */
    TaskListAgg findById(Long id);

    /**
     * 根据名称查询任务清单
     *
     * @param name 清单名称
     * @return 任务清单列表
     */
    List<TaskListAgg> findByName(String name);

    /**
     * 根据创建人查询任务清单
     *
     * @param createUserId 创建人ID
     * @return 任务清单列表
     */
    List<TaskListAgg> findByCreateUserId(String createUserId);

    /**
     * 删除任务清单
     *
     * @param taskListAgg 任务清单聚合
     */
    void delete(TaskListAgg taskListAgg);
}