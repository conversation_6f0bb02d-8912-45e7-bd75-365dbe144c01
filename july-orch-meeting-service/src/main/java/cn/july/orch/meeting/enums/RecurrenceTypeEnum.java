package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 重复类型枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum RecurrenceTypeEnum {

    DAILY(0, "每天"),
    WEEKLY(1, "每周"),
    MONTHLY(2, "每月"),
    YEARLY(3, "每年");

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, RecurrenceTypeEnum> VALUES = new HashMap<>();

    static {
        for (final RecurrenceTypeEnum item : RecurrenceTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RecurrenceTypeEnum of(int code) {
        return VALUES.get(code);
    }
}
