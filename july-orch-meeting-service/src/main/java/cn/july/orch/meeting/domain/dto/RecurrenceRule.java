package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.RecurrenceTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description 重复规则模型
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecurrenceRule {

    @ApiModelProperty(value = "重复类型", required = true)
    @NotNull(message = "重复类型不能为空")
    private RecurrenceTypeEnum recurrenceType;

    @ApiModelProperty(value = "重复间隔(每N天/周/月)", example = "1")
    @Min(value = 1, message = "重复间隔必须大于0")
    private Integer recurrenceInterval;

    @ApiModelProperty(value = "每周重复的星期几(1=周日,2=周一,...,7=周六)")
    private List<Integer> recurrenceWeekdays;

    @ApiModelProperty(value = "每月重复的日期列表(1-31)")
    private List<Integer> recurrenceMonthDays;

    @ApiModelProperty(value = "重复结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate recurrenceEndDate;
}
