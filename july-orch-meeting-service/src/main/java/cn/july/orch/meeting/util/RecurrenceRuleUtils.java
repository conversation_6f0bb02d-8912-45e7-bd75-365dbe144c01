package cn.july.orch.meeting.util;

import cn.july.orch.meeting.enums.RecurrenceTypeEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 重复性规则工具类
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public class RecurrenceRuleUtils {

    /**
     * 解析星期几字符串为整数列表
     * 
     * @param weekdaysStr 星期几字符串，格式："1,2,3" (1=周日,2=周一,...,7=周六)
     * @return 星期几列表
     */
    public static List<Integer> parseWeekdays(String weekdaysStr) {
        if (weekdaysStr == null || weekdaysStr.isEmpty()) {
            return List.of();
        }
        return Arrays.stream(weekdaysStr.split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    /**
     * 解析月日期字符串为整数列表
     * 
     * @param monthDaysStr 月日期字符串，格式："1,15,30"
     * @return 月日期列表
     */
    public static List<Integer> parseMonthDays(String monthDaysStr) {
        if (monthDaysStr == null || monthDaysStr.isEmpty()) {
            return List.of();
        }
        return Arrays.stream(monthDaysStr.split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    /**
     * 将整数列表转换为逗号分隔的字符串
     * 
     * @param list 整数列表
     * @return 逗号分隔的字符串
     */
    public static String integerListToString(List<Integer> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    /**
     * 计算年重复的下次时间
     * 
     * @param baseTime 基准时间
     * @param interval 间隔年数
     * @return 下次时间
     */
    public static LocalDateTime calculateNextYearlyTime(LocalDateTime baseTime, int interval) {
        LocalDateTime nextYear = baseTime.plusYears(interval);
        
        // 处理2月29日等特殊日期
        LocalDate originalDate = baseTime.toLocalDate();
        LocalDate nextYearDate = nextYear.toLocalDate();
        
        // 如果原始日期是2月29日，但下一年不是闰年，则使用2月28日
        if (originalDate.getMonthValue() == 2 && originalDate.getDayOfMonth() == 29) {
            if (!nextYearDate.isLeapYear()) {
                nextYear = nextYear.withDayOfMonth(28);
            }
        }
        
        return nextYear;
    }

    /**
     * 计算月重复多选的下次时间
     * 
     * @param baseTime 基准时间
     * @param monthDays 月日期列表
     * @param interval 间隔月数
     * @return 下次时间
     */
    public static LocalDateTime calculateNextMonthlyMultiTime(LocalDateTime baseTime, List<Integer> monthDays, int interval) {
        if (monthDays.isEmpty()) {
            // 没有指定日期，按间隔月数计算
            return baseTime.plusMonths(interval);
        }
        
        // 按指定日期计算
        LocalDateTime currentTime = baseTime;
        int currentDay = currentTime.getDayOfMonth();
        
        // 找到下一个重复的日期
        for (int monthDay : monthDays) {
            if (monthDay > currentDay) {
                // 当月还有这个日期
                LocalDateTime nextTime = currentTime.withDayOfMonth(monthDay);
                if (nextTime.isAfter(baseTime)) {
                    return nextTime;
                }
            }
        }
        
        // 当月没有合适的日期，找下个月
        LocalDateTime nextMonth = currentTime.plusMonths(interval);
        int nextMonthDay = monthDays.get(0); // 取第一个日期
        int maxDay = nextMonth.toLocalDate().lengthOfMonth();
        int actualDay = Math.min(nextMonthDay, maxDay);
        
        return nextMonth.withDayOfMonth(actualDay);
    }

    /**
     * 验证重复类型是否支持
     * 
     * @param recurrenceType 重复类型
     * @return 是否支持
     */
    public static boolean isRecurrenceTypeSupported(RecurrenceTypeEnum recurrenceType) {
        return recurrenceType != null && (
                recurrenceType == RecurrenceTypeEnum.DAILY ||
                recurrenceType == RecurrenceTypeEnum.WEEKLY ||
                recurrenceType == RecurrenceTypeEnum.MONTHLY ||
                recurrenceType == RecurrenceTypeEnum.YEARLY
        );
    }

    /**
     * 验证月日期是否有效
     * 
     * @param monthDay 月日期
     * @return 是否有效
     */
    public static boolean isValidMonthDay(int monthDay) {
        return monthDay >= 1 && monthDay <= 31;
    }

    /**
     * 验证星期几是否有效
     * 
     * @param weekday 星期几
     * @return 是否有效
     */
    public static boolean isValidWeekday(int weekday) {
        return weekday >= 1 && weekday <= 7;
    }
}
