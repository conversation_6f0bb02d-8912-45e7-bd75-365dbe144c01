package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.core.model.ddd.IdQuery;
import cn.july.orch.meeting.config.FeishuTaskProperties;
import cn.july.orch.meeting.domain.command.TaskCreateCommand;
import cn.july.orch.meeting.domain.command.TaskUpdateCommand;
import cn.july.orch.meeting.domain.param.TaskCreateParam;
import cn.july.orch.meeting.domain.param.TaskUpdateParam;
import cn.july.orch.meeting.domain.command.TaskDecomposeCommand;
import cn.july.orch.meeting.domain.dto.TaskDTO;
import cn.july.orch.meeting.domain.po.TaskPO;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.mapper.TaskMapper;
import cn.july.orch.meeting.feishu.FeishuApiService;
import cn.july.orch.meeting.feishu.req.CreateTaskRequest;
import cn.july.orch.meeting.feishu.req.UpdateTaskRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lark.oapi.service.task.v2.model.CreateTaskRespBody;
import com.lark.oapi.service.task.v2.model.Member;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Objects;

/**
 * <AUTHOR> Assistant
 * @description 任务操作服务
 */
@Slf4j
@Service
public class TaskActionService {

    @Resource
    private TaskDomainService taskDomainService;
    @Resource
    private FeishuApiService feishuApiService;
    @Resource
    private TaskQueryService taskQueryService;
    @Resource
    private FeishuTaskProperties feishuTaskProperties;
    @Resource
    private TaskMapper taskMapper;

    /**
     * 创建任务
     *
     * @param command 任务创建命令
     * @return 任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(TaskCreateCommand command) {
        log.info("创建任务，标题：{}，任务清单ID：{}", command.getTitle(), command.getTaskListId());

        // 1. 先创建本地任务
        TaskCreateParam param = TaskCreateParam.builder()
            .title(command.getTitle())
            .description(command.getDescription())
            .ownerOpenId(command.getOwnerOpenId())
            .ownerName(command.getOwnerName())
            .priority(command.getPriority())
            .dueDate(command.getDueDate())
            .taskListId(command.getTaskListId())
            .parentId(command.getParentId())
            .meetingId(command.getMeetingId())
            .attachmentKeys(command.getAttachmentKeys())
            .build();
        
        Long taskId = taskDomainService.create(param);

        // 2. 根据配置决定是否同步飞书任务
        if (feishuTaskProperties.isSyncEnabled()) {
            syncToFeishu(taskId, command);
        }

        return taskId;
    }

    /**
     * 更新任务
     *
     * @param command 任务命令
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskUpdateCommand command) {
        log.info("更新任务，ID：{}，标题：{}", command.getId(), command.getTitle());

        TaskUpdateParam param = TaskUpdateParam.builder()
            .taskId(command.getId())
            .title(command.getTitle())
            .description(command.getDescription())
            .ownerOpenId(command.getOwnerOpenId())
            .ownerName(command.getOwnerName())
            .priority(command.getPriority())
            .dueDate(command.getDueDate())
            .meetingId(command.getMeetingId())
            .build();
        
        taskDomainService.update(param);
    }

    /**
     * 删除任务
     *
     * @param idQuery ID查询
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(IdQuery idQuery) {
        log.info("删除任务，ID：{}", idQuery.getId());
        TaskDTO taskDTO = taskQueryService.detail(idQuery);
        
        taskDomainService.delete(idQuery.getId());
        
        feishuApiService.deleteTask(taskDTO.getFeishuTaskId());
    }

    /**
     * 完成任务
     *
     * @param idQuery ID查询
     */
    @Transactional(rollbackFor = Exception.class)
    public void complete(IdQuery idQuery) {
        log.info("完成任务，ID：{}", idQuery.getId());
        
        // 校验子任务是否全部完成
        validateSubTasksCompletion(idQuery.getId());
        
        taskDomainService.complete(idQuery.getId());
        
        TaskDTO taskDTO = taskQueryService.detail(idQuery);
        feishuApiService.patchTask(UpdateTaskRequest.builder()
            .taskGuid(taskDTO.getFeishuTaskId())
            .completedTime(LocalDateTime.now())
            .build());
    }

    /**
     * 开始任务
     *
     * @param idQuery ID查询
     */
    @Transactional(rollbackFor = Exception.class)
    public void start(IdQuery idQuery) {
        log.info("开始任务，ID：{}", idQuery.getId());
        
        taskDomainService.start(idQuery.getId());
    }

    /**
     * 更新飞书任务ID
     *
     * @param taskId 任务ID
     * @param feishuTaskId 飞书任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFeishuTaskId(Long taskId, String feishuTaskId) {
        log.info("更新飞书任务ID，任务ID：{}，飞书任务ID：{}", taskId, feishuTaskId);
        taskDomainService.updateFeishuTaskId(taskId, feishuTaskId);
    }

    /**
     * 处理超期任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void processOverdueTasks() {
        log.info("处理超期任务");
        taskDomainService.processOverdueTasks();
    }

    /**
     * 任务拆解（同时创建多个子任务）
     *
     * @param command 任务拆解命令
     * @return 子任务ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Long> decompose(TaskDecomposeCommand command) {
        log.info("开始任务拆解，父任务ID：{}，子任务数量：{}", 
            command.getParentTaskId(), command.getSubTasks().size());

        List<Long> subTaskIds = new ArrayList<>();

        // 验证父任务是否存在
        TaskDTO parentTask = taskQueryService.detail(IdQuery.builder().id(command.getParentTaskId()).build());
        if (parentTask == null) {
            throw new BusinessException("父任务不存在，ID：" + command.getParentTaskId());
        }

        // 批量创建子任务
        for (TaskDecomposeCommand.SubTaskInfo subTaskInfo : command.getSubTasks()) {
            try {
                // 直接创建子任务（带附件）
                TaskCreateParam param = TaskCreateParam.builder()
                    .title(subTaskInfo.getTitle())
                    .description(subTaskInfo.getDescription())
                    .ownerOpenId(subTaskInfo.getOwnerOpenId())
                    .ownerName(subTaskInfo.getOwnerName())
                    .priority(subTaskInfo.getPriority() != null ? subTaskInfo.getPriority() : parentTask.getPriority())
                    .dueDate(subTaskInfo.getDueDate() != null ? subTaskInfo.getDueDate() : parentTask.getDueDate())
                    .taskListId(parentTask.getTaskListId())
                    .parentId(command.getParentTaskId())
                    .meetingId(parentTask.getMeetingId())
                    .attachmentKeys(subTaskInfo.getAttachmentKeys())
                    .build();
                
                Long subTaskId = taskDomainService.create(param);

                subTaskIds.add(subTaskId);
                
                // 根据配置决定是否同步子任务到飞书
                if (feishuTaskProperties.isSyncEnabled()) {
                    syncSubTaskToFeishu(subTaskId, subTaskInfo, parentTask);
                }
                
                log.info("子任务创建成功，标题：{}，ID：{}", subTaskInfo.getTitle(), subTaskId);
                
            } catch (Exception e) {
                log.error("子任务创建失败，标题：{}", subTaskInfo.getTitle(), e);
                throw new BusinessException("子任务创建失败：" + subTaskInfo.getTitle());
            }
        }

        // 记录任务拆解动态（切面会自动处理）

        log.info("任务拆解完成，父任务ID：{}，子任务数量：{}", 
            command.getParentTaskId(), subTaskIds.size());

        return subTaskIds;
    }

    /**
     * 同步任务到飞书
     */
    private void syncToFeishu(Long taskId, TaskCreateCommand command) {
        try {
            Member member = Member.newBuilder()
                    .id(command.getOwnerOpenId())
                    .role("assignee")
                    .type("user")
                    .name(command.getOwnerName())
                    .build();

            CreateTaskRespBody createTaskResp = feishuApiService.createTask(CreateTaskRequest.builder()
                    .summary(command.getTitle())
                    .description(command.getDescription())
                    .dueTime(command.getDueDate())
                    .members(Collections.singletonList(member))
                    .build());

            if (createTaskResp != null && createTaskResp.getTask() != null) {
                String feishuTaskGuid = createTaskResp.getTask().getGuid();
                if (feishuTaskGuid != null) {
                    taskDomainService.updateFeishuTaskId(taskId, feishuTaskGuid);
                    log.info("任务同步飞书成功，本地任务ID：{}，飞书任务GUID：{}", taskId, feishuTaskGuid);
                }
            }
        } catch (Exception e) {
            log.error("同步任务到飞书失败，任务ID：{}，错误：{}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 同步子任务到飞书
     */
    private void syncSubTaskToFeishu(Long subTaskId, TaskDecomposeCommand.SubTaskInfo subTaskInfo, TaskDTO parentTask) {
        try {
            Member member = Member.newBuilder()
                    .id(subTaskInfo.getOwnerOpenId())
                    .role("assignee")
                    .type("user")
                    .name(subTaskInfo.getOwnerName())
                    .build();

            CreateTaskRespBody createTaskResp = feishuApiService.createTask(CreateTaskRequest.builder()
                    .summary(subTaskInfo.getTitle())
                    .description(subTaskInfo.getDescription())
                    .dueTime(subTaskInfo.getDueDate() != null ? subTaskInfo.getDueDate() : parentTask.getDueDate())
                    .members(Collections.singletonList(member))
                    .build());

            if (createTaskResp != null && createTaskResp.getTask() != null) {
                String feishuTaskGuid = createTaskResp.getTask().getGuid();
                if (feishuTaskGuid != null) {
                    taskDomainService.updateFeishuTaskId(subTaskId, feishuTaskGuid);
                    log.info("子任务同步飞书成功，本地任务ID：{}，飞书任务GUID：{}", subTaskId, feishuTaskGuid);
                }
            }
        } catch (Exception e) {
            log.error("同步子任务到飞书失败，子任务ID：{}，错误：{}", subTaskId, e.getMessage(), e);
        }
    }
    
    /**
     * 校验子任务是否全部完成
     *
     * @param taskId 任务ID
     */
    private void validateSubTasksCompletion(Long taskId) {
        // 查询所有子任务
        LambdaQueryWrapper<TaskPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskPO::getParentId, taskId)
                   .eq(TaskPO::getDeleted, cn.july.core.model.enums.DeletedEnum.NOT_DELETED);
        
        List<TaskPO> subTasks = taskMapper.selectList(queryWrapper);
        
        if (subTasks.isEmpty()) {
            // 没有子任务，可以直接完成
            return;
        }
        
        // 检查是否有未完成的子任务
        List<TaskPO> incompleteTasks = subTasks.stream()
                .filter(task -> !TaskStatusEnum.COMPLETED.equals(task.getStatus()))
                .collect(ArrayList::new, (list, task) -> list.add(task), ArrayList::addAll);
        
        if (!incompleteTasks.isEmpty()) {
            String incompleteTaskTitles = incompleteTasks.stream()
                    .map(TaskPO::getTitle)
                    .collect(Collectors.joining("、"));
            
            throw new BusinessException(
                String.format("任务存在未完成的子任务，无法完成父任务。未完成的子任务：%s", incompleteTaskTitles)
            );
        }
        
        log.info("子任务完成校验通过，任务ID：{}，子任务数量：{}", taskId, subTasks.size());
    }
}