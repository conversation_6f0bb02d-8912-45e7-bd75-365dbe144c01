package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;

import cn.july.orch.meeting.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议列表DTO
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewMeetingListDTO {

    @ApiModelProperty(value = "会议ID")
    private Long id;

    @ApiModelProperty(value = "会议名称")
    private String meetingName;

    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "会议结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "会议状态")
    private NewMeetingStatusEnum status;

    @ApiModelProperty(value = "优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty(value = "会议地点")
    private String meetingLocation;

    @ApiModelProperty(value = "参会人数")
    private Integer attendeeCount;

    @ApiModelProperty(value = "参会人员列表（用户ID列表）")
    private List<String> attendees;

    @ApiModelProperty(value = "参会人员详细信息列表")
    private List<FSUserInfoDTO> attendeeDetails;

    @ApiModelProperty(value = "主持人ID")
    private String hostUserId;

    @ApiModelProperty(value = "主持人详细信息")
    private FSUserInfoDTO hostUserDetail;

    @ApiModelProperty(value = "记录员ID")
    private String recorderUserId;

    @ApiModelProperty(value = "记录员详细信息")
    private FSUserInfoDTO recorderUserDetail;

    @ApiModelProperty(value = "会议链接")
    private String meetingUrl;

    @ApiModelProperty(value = "妙计链接")
    private String minuteUrl;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "是否重复会议")
    private IsRecurringEnum isRecurring;

    @ApiModelProperty(value = "重复类型")
    private RecurrenceTypeEnum recurrenceType;

    @ApiModelProperty(value = "重复间隔")
    private Integer recurrenceInterval;

    @ApiModelProperty(value = "每周重复的星期几")
    private String recurrenceWeekdays;

    @ApiModelProperty(value = "每月重复的日期列表")
    private List<Integer> recurrenceMonthDays;

    @ApiModelProperty(value = "重复结束日期")
    private LocalDate recurrenceEndDate;

    @ApiModelProperty(value = "父会议ID")
    private Long parentMeetingId;

    @ApiModelProperty(value = "实例序号")
    private Integer instanceSequence;

    @ApiModelProperty(value = "会议标签列表")
    private List<SimpleMeetingTagDTO> meetingTags;

    @ApiModelProperty(value = "重复系列是否已取消")
    private Integer recurrenceCancelled;

    @ApiModelProperty(value = "会前文档列表")
    private List<PreMeetingDocumentDTO> preMeetingDocuments;

    @ApiModelProperty(value = "会前文档详细信息列表")
    private List<FileInfoDTO> preMeetingDocumentDetails;

    @ApiModelProperty(value = "实际会议开始时间")
    private LocalDateTime actualStartTime;

    @ApiModelProperty(value = "实际会议结束时间")
    private LocalDateTime actualEndTime;

    @ApiModelProperty(value = "实际参会人员open_id列表")
    private List<String> actualAttendees;

    @ApiModelProperty(value = "实际参会人员详细信息列表")
    private List<FSUserInfoDTO> actualAttendeeDetails;

    @ApiModelProperty(value = "会议分析报告状态")
    private ReportStatusEnum reportStatus;

    @ApiModelProperty(value = "AI纪要状态")
    private ReportStatusEnum aiTranscriptStatus;
}
