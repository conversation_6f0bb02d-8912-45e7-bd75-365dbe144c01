package cn.july.orch.meeting.repository;

import cn.july.orch.meeting.assembler.TaskListAssembler;
import cn.july.orch.meeting.domain.entity.TaskListAgg;
import cn.july.orch.meeting.domain.entity.TaskListInfo;
import cn.july.orch.meeting.domain.po.TaskListPO;
import cn.july.orch.meeting.mapper.TaskListMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务清单仓储实现
 */
@Slf4j
@Repository
public class TaskListRepositoryImpl implements ITaskListRepository {

    @Resource
    private TaskListMapper taskListMapper;
    @Resource
    private TaskListAssembler taskListAssembler;

    @Override
    public Long insert(TaskListAgg taskListAgg) {
        log.debug("插入任务清单，名称：{}", taskListAgg.getInfo().getName());
        
        TaskListPO taskListPO = taskListAssembler.toTaskListPO(taskListAgg.getInfo());
        taskListMapper.insert(taskListPO);
        
        // 设置生成的ID到聚合对象中
        taskListAgg.getInfo().setId(taskListPO.getId());
        
        log.debug("任务清单插入成功，ID：{}", taskListPO.getId());
        return taskListPO.getId();
    }

    @Override
    public void update(TaskListAgg taskListAgg) {
        log.debug("更新任务清单，ID：{}", taskListAgg.getInfo().getId());
        
        TaskListPO taskListPO = taskListAssembler.toTaskListPO(taskListAgg.getInfo());
        taskListMapper.updateById(taskListPO);
        
        log.debug("任务清单更新成功，ID：{}", taskListPO.getId());
    }

    @Override
    public TaskListAgg findById(Long id) {
        log.debug("根据ID查询任务清单，ID：{}", id);
        
        TaskListPO taskListPO = taskListMapper.selectById(id);
        if (taskListPO == null) {
            log.debug("任务清单不存在，ID：{}", id);
            return null;
        }
        
        TaskListInfo taskListInfo = taskListAssembler.toTaskListInfo(taskListPO);
        return TaskListAgg.builder()
                .info(taskListInfo)
                .build();
    }

    @Override
    public List<TaskListAgg> findByName(String name) {
        log.debug("根据名称查询任务清单，名称：{}", name);
        
        List<TaskListPO> taskListPOList = taskListMapper.findByName(name);
        
        return taskListPOList.stream()
                .map(taskListPO -> TaskListAgg.builder()
                        .info(taskListAssembler.toTaskListInfo(taskListPO))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<TaskListAgg> findByCreateUserId(String createUserId) {
        log.debug("根据创建人ID查询任务清单，创建人ID：{}", createUserId);
        
        List<TaskListPO> taskListPOList = taskListMapper.findByCreateUserId(createUserId);
        
        return taskListPOList.stream()
                .map(taskListPO -> TaskListAgg.builder()
                        .info(taskListAssembler.toTaskListInfo(taskListPO))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public void delete(TaskListAgg taskListAgg) {
        log.debug("删除任务清单，ID：{}", taskListAgg.getInfo().getId());
        
        // 软删除：更新deleted字段
        TaskListPO taskListPO = taskListAssembler.toTaskListPO(taskListAgg.getInfo());
        taskListMapper.updateById(taskListPO);
        
        log.debug("任务清单删除成功，ID：{}", taskListPO.getId());
    }
}