package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.command.MeetingTagCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingTagUpdateCommand;
import cn.july.orch.meeting.domain.dto.MeetingTagDTO;
import cn.july.orch.meeting.domain.entity.MeetingTag;
import cn.july.orch.meeting.domain.po.MeetingTagPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标签转换器
 * @date 2025-08-26
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingTagAssembler {

    MeetingTagPO toPO(MeetingTag meetingTag);

    MeetingTag toEntity(MeetingTagPO meetingTagPO);

    MeetingTagDTO toDTO(MeetingTag meetingTag);

    List<MeetingTagDTO> toDTOList(List<MeetingTag> meetingTags);

    MeetingTag toEntity(MeetingTagCreateCommand command);

    MeetingTag toEntity(MeetingTagUpdateCommand command);
}