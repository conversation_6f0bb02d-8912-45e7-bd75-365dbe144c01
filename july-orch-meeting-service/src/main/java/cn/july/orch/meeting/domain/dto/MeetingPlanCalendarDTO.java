package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.RecurrenceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议规划日历DTO
 * @date 2025-01-24
 */
@Data
public class MeetingPlanCalendarDTO {

    @ApiModelProperty(value = "会议规划ID")
    private Long id;

    @ApiModelProperty(value = "会议规划名称")
    private String planName;

    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime plannedStartTime;

    @ApiModelProperty(value = "计划结束时间")
    private LocalDateTime plannedEndTime;

    @ApiModelProperty(value = "计划持续时长(分钟)")
    private Integer plannedDuration;

    @ApiModelProperty(value = "会议规划状态")
    private MeetingPlanStatusEnum status;

    @ApiModelProperty(value = "会议标准名称")
    private String meetingStandardName;

    @ApiModelProperty(value = "优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "会议地点")
    private String meetingLocation;

    @ApiModelProperty(value = "参会人数")
    private Integer attendeeCount;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "日历背景色")
    private String backgroundColor;

    @ApiModelProperty(value = "是否重复会议")
    private IsRecurringEnum isRecurring;

    @ApiModelProperty(value = "重复类型")
    private RecurrenceTypeEnum recurrenceType;

    @ApiModelProperty(value = "重复间隔")
    private Integer recurrenceInterval;

    @ApiModelProperty(value = "每周重复的星期几")
    private String recurrenceWeekdays;

    @ApiModelProperty(value = "每月重复的日期列表")
    private String recurrenceMonthDays;

    @ApiModelProperty(value = "重复结束日期")
    private LocalDate recurrenceEndDate;

    @ApiModelProperty(value = "是否为重复实例")
    private Boolean isRecurringInstance;

    @ApiModelProperty(value = "原始会议规划ID")
    private Long originalPlanId;
}
