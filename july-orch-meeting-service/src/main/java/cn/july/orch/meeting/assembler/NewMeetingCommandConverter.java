package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.command.NewMeetingCreateCommand;
import cn.july.orch.meeting.domain.command.NewMeetingUpdateCommand;
import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.service.FileDetailService;
import cn.july.orch.meeting.service.MeetingTagService;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.domain.dto.MeetingTagDTO;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 新会议命令转换器 - 专门处理 Command 到 Entity 的复杂转换
 */
@Component
public class NewMeetingCommandConverter {

    @Resource
    private MeetingTagService meetingTagService;
    
    @Resource
    private FileDetailService fileDetailService;

    /**
     * 将创建命令转换为新会议实体（完整版）
     * 根据 ID 列表自动构建完整的 meetingTags 和 preMeetingDocuments
     * 
     * @param command 创建命令
     * @return 新会议实体
     */
    public NewMeeting toEntityWithCompleteData(NewMeetingCreateCommand command) {
        if (command == null) {
            return null;
        }

        return NewMeeting.builder()
            .meetingName(command.getMeetingName())
            .meetingDescription(command.getMeetingDescription())
            .meetingPlanId(command.getMeetingPlanId())
            .meetingStandardId(command.getMeetingStandardId())
            .startTime(command.getStartTime())
            .endTime(command.getEndTime())
            .priorityLevel(command.getPriorityLevel())
            .meetingLocation(command.getMeetingLocation())
            .attendees(command.getAttendees())
            .hostUserId(command.getHostUserId())
            .recorderUserId(command.getRecorderUserId())
            .enableDocAiSummary(command.getEnableDocAiSummary())
            // 根据 ID 列表构建完整对象
            .meetingTags(buildMeetingTagsFromIds(command.getMeetingTagIds()))
            .preMeetingDocuments(buildPreMeetingDocumentsFromKeys(command.getPreMeetingDocumentKeys()))
            .build();
    }

    /**
     * 将更新命令转换为新会议实体（完整版）
     * 
     * @param command 更新命令
     * @return 新会议实体
     */
    public NewMeeting toEntityWithCompleteData(NewMeetingUpdateCommand command) {
        if (command == null) {
            return null;
        }

        return NewMeeting.builder()
            .id(command.getId())
            .meetingName(command.getMeetingName())
            .meetingDescription(command.getMeetingDescription())
            .startTime(command.getStartTime())
            .endTime(command.getEndTime())
            .priorityLevel(command.getPriorityLevel())
            .meetingLocation(command.getMeetingLocation())
            .attendees(command.getAttendees())
            .hostUserId(command.getHostUserId())
            .recorderUserId(command.getRecorderUserId())
            // preMeetingDocuments 根据命令中的文档key列表构建
            .preMeetingDocuments(buildPreMeetingDocumentsFromKeys(command.getPreMeetingDocuments()))
            .build();
    }

    /**
     * 映射方法：List<String> -> List<PreMeetingDocumentDTO>
     * 这就是 MapStruct 无法自动处理的复杂映射
     * 
     * @param documentKeys 文档 Key 列表
     * @return PreMeetingDocumentDTO 列表
     */
    @Named("mapPreMeetingDocumentKeys")
    public List<PreMeetingDocumentDTO> mapPreMeetingDocumentKeys(List<String> documentKeys) {
        return buildPreMeetingDocumentsFromKeys(documentKeys);
    }

    /**
     * 映射方法：List<Long> -> List<SimpleMeetingTagDTO>
     * 这也是 MapStruct 需要的自定义映射方法
     * 
     * @param meetingTagIds 会议标签 ID 列表
     * @return SimpleMeetingTagDTO 列表
     */
    @Named("mapMeetingTagIds")
    public List<SimpleMeetingTagDTO> mapMeetingTagIds(List<Long> meetingTagIds) {
        return buildMeetingTagsFromIds(meetingTagIds);
    }

    /**
     * 根据会议标签 ID 列表构建 SimpleMeetingTagDTO 列表
     * 符合项目规范：使用轻量级对象避免歧义
     * 
     * @param meetingTagIds 会议标签 ID 列表
     * @return SimpleMeetingTagDTO 列表
     */
    private List<SimpleMeetingTagDTO> buildMeetingTagsFromIds(List<Long> meetingTagIds) {
        if (CollectionUtils.isEmpty(meetingTagIds)) {
            return new ArrayList<>();
        }

        List<SimpleMeetingTagDTO> meetingTags = new ArrayList<>();
        for (Long tagId : meetingTagIds) {
            try {
                MeetingTagDTO fullTag = meetingTagService.getById(tagId);
                if (fullTag != null) {
                    // 转换为SimpleMeetingTagDTO（轻量级）
                    SimpleMeetingTagDTO tag = SimpleMeetingTagDTO.builder()
                        .id(fullTag.getId())
                        .name(fullTag.getName())
                        .color(fullTag.getColor())
                        .build();
                    meetingTags.add(tag);
                }
            } catch (Exception e) {
                // 忽略单个标签获取失败的情况，继续处理其他标签
            }
        }
        return meetingTags;
    }

    /**
     * 根据文档 Key 列表构建 PreMeetingDocumentDTO 列表
     * 符合项目规范：从 filekey 获取完整文档信息
     * 
     * @param documentKeys 文档 Key 列表
     * @return PreMeetingDocumentDTO 列表
     */
    private List<PreMeetingDocumentDTO> buildPreMeetingDocumentsFromKeys(List<String> documentKeys) {
        if (CollectionUtils.isEmpty(documentKeys)) {
            return new ArrayList<>();
        }

        List<PreMeetingDocumentDTO> documents = new ArrayList<>();
        for (String fileKey : documentKeys) {
            try {
                FileDetailPO fileDetail = fileDetailService.getById(fileKey);
                if (fileDetail != null) {
                    PreMeetingDocumentDTO document = PreMeetingDocumentDTO.builder()
                        .fileKey(fileKey)
                        .fileName(fileDetail.getOriginalFilename())
                        .fileSize(fileDetail.getSize())
                        .fileType(fileDetail.getContentType())
                        .aiSummaryStatus("PENDING") // 初始状态
                        .aiSummaryContent("")       // 初始内容为空
                        .build();
                    documents.add(document);
                }
            } catch (Exception e) {
                // 忽略单个文档获取失败的情况，继续处理其他文档
            }
        }
        return documents;
    }
}