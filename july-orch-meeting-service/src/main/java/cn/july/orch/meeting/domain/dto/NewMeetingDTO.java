package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;

import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.RecurrenceTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewMeetingDTO {

    @ApiModelProperty(value = "会议ID")
    private Long id;

    @ApiModelProperty(value = "会议名称")
    private String meetingName;

    @ApiModelProperty(value = "会议描述")
    private String meetingDescription;

    @ApiModelProperty(value = "会议规划ID")
    private Long meetingPlanId;

    @ApiModelProperty(value = "会议标准ID")
    private Long meetingStandardId;

    @ApiModelProperty(value = "会议编号")
    private String meetingNo;

    @ApiModelProperty(value = "会议开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "会议结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "会议状态")
    private NewMeetingStatusEnum status;

    @ApiModelProperty(value = "优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty(value = "会议地点")
    private String meetingLocation;

    @ApiModelProperty(value = "会议室ID")
    private Long meetingRoomId;

    @ApiModelProperty(value = "参会人员列表（用户ID列表）")
    private List<String> attendees;

    @ApiModelProperty(value = "参会人员详细信息列表")
    private List<FSUserInfoDTO> attendeeDetails;

    @ApiModelProperty(value = "主持人ID")
    private String hostUserId;

    @ApiModelProperty(value = "主持人详细信息")
    private FSUserInfoDTO hostUserDetail;

    @ApiModelProperty(value = "记录员ID")
    private String recorderUserId;

    @ApiModelProperty(value = "记录员详细信息")
    private FSUserInfoDTO recorderUserDetail;

    @ApiModelProperty(value = "飞书日程事件ID")
    private String fsCalendarEventId;

    @ApiModelProperty(value = "飞书会议ID")
    private String fsMeetingId;

    @ApiModelProperty(value = "会议链接")
    private String meetingUrl;

    @ApiModelProperty(value = "妙计链接")
    private String minuteUrl;

    @ApiModelProperty(value = "创建人ID")
    private String createUserId;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否重复会议")
    private IsRecurringEnum isRecurring;

    @ApiModelProperty(value = "重复类型")
    private RecurrenceTypeEnum recurrenceType;

    @ApiModelProperty(value = "重复间隔")
    private Integer recurrenceInterval;

    @ApiModelProperty(value = "每周重复的星期几")
    private String recurrenceWeekdays;

    @ApiModelProperty(value = "每月重复的日期列表")
    private List<Integer> recurrenceMonthDays;

    @ApiModelProperty(value = "重复结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate recurrenceEndDate;

    @ApiModelProperty(value = "父会议ID")
    private Long parentMeetingId;

    @ApiModelProperty(value = "实例序号")
    private Integer instanceSequence;

    @ApiModelProperty(value = "是否启用文档AI汇总")
    private Boolean enableDocAiSummary;

    @ApiModelProperty(value = "会议标签列表")
    private List<SimpleMeetingTagDTO> meetingTags;

    @ApiModelProperty(value = "重复系列是否已取消")
    private Integer recurrenceCancelled;

    @ApiModelProperty(value = "会前文档列表")
    private List<PreMeetingDocumentDTO> preMeetingDocuments;

    @ApiModelProperty(value = "会前文档详细信息列表")
    private List<FileInfoDTO> preMeetingDocumentDetails;

    @ApiModelProperty(value = "实际会议开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime actualStartTime;

    @ApiModelProperty(value = "实际会议结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime actualEndTime;

    @ApiModelProperty(value = "实际参会人员open_id列表")
    private List<String> actualAttendees;

    @ApiModelProperty(value = "实际参会人员详细信息列表")
    private List<FSUserInfoDTO> actualAttendeeDetails;
}