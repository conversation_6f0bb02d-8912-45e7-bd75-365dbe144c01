package cn.july.orch.meeting.domain.entity;

import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.RecurrenceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 新会议领域实体
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewMeeting {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议名称
     */
    private String meetingName;

    /**
     * 会议描述
     */
    private String meetingDescription;

    /**
     * 会议规划ID
     */
    private Long meetingPlanId;

    /**
     * 会议标准ID
     */
    private Long meetingStandardId;

    /**
     * 会议编号
     */
    private String meetingNo;

    /**
     * 会议开始时间
     */
    private LocalDateTime startTime;

    /**
     * 会议结束时间
     */
    private LocalDateTime endTime;

    /**
     * 会议状态
     */
    private NewMeetingStatusEnum status;

    /**
     * 优先级
     */
    private PriorityLevelEnum priorityLevel;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 会议室ID
     */
    private Long meetingRoomId;

    /**
     * 参会人员列表（用户ID列表，与会议规划保持一致）
     */
    private List<String> attendees;

    /**
     * 主持人ID
     */
    private String hostUserId;

    /**
     * 记录员ID
     */
    private String recorderUserId;

    /**
     * 飞书日程事件ID
     */
    private String fsCalendarEventId;

    /**
     * 飞书会议ID
     */
    private String fsMeetingId;

    /**
     * 会议链接
     */
    private String meetingUrl;

    /**
     * 妙计链接
     */
    private String minuteUrl;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否重复会议
     */
    private IsRecurringEnum isRecurring;

    /**
     * 重复类型
     */
    private RecurrenceTypeEnum recurrenceType;

    /**
     * 重复间隔
     */
    private Integer recurrenceInterval;

    /**
     * 每周重复的星期几
     */
    private String recurrenceWeekdays;

    /**
     * 每月重复的日期列表(存储格式: "1,15,30")
     */
    private List<Integer> recurrenceMonthDays;

    /**
     * 重复结束日期
     */
    private LocalDate recurrenceEndDate;

    /**
     * 父会议ID
     */
    private Long parentMeetingId;

    /**
     * 实例序号
     */
    private Integer instanceSequence;

    /**
     * 是否启用文档AI汇总
     */
    private Boolean enableDocAiSummary;

    /**
     * 会议标签列表
     */
    private List<SimpleMeetingTagDTO> meetingTags;

    /**
     * 检查是否可以生成下次会议
     */
    public boolean canGenerateNextMeeting() {
        if (isRecurring == null || isRecurring == IsRecurringEnum.NO) {
            return false;
        }
        
        // 检查重复系列是否已取消
        if (recurrenceCancelled != null && recurrenceCancelled == 1) {
            return false;
        }
        
        // 检查结束日期
        if (recurrenceEndDate != null && LocalDate.now().isAfter(recurrenceEndDate)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算下次会议时间
     */
    public LocalDateTime calculateNextMeetingTime() {
        if (!canGenerateNextMeeting()) {
            return null;
        }
        
        LocalDateTime baseTime = endTime != null ? endTime : startTime;
        int interval = recurrenceInterval != null ? recurrenceInterval : 1;
        
        switch (recurrenceType) {
            case DAILY:
                return baseTime.plusDays(interval);
                
            case WEEKLY:
                if (recurrenceWeekdays != null && !recurrenceWeekdays.isEmpty()) {
                    return calculateNextWeeklyTime(baseTime);
                } else {
                    return baseTime.plusWeeks(interval);
                }
                
            case MONTHLY:
                if (recurrenceMonthDays != null && !recurrenceMonthDays.isEmpty()) {
                    return calculateNextMonthlyTime(baseTime);
                } else {
                    return baseTime.plusMonths(interval);
                }
                
            case YEARLY:
                return calculateNextYearlyTime(baseTime);
                
            default:
                return baseTime.plusDays(1);
        }
    }

    private LocalDateTime calculateNextWeeklyTime(LocalDateTime baseTime) {
        List<Integer> weekdays = parseWeekdays(recurrenceWeekdays);
        int currentWeekday = baseTime.getDayOfWeek().getValue();
        int interval = recurrenceInterval != null ? recurrenceInterval : 1;
        
        // 如果指定了星期几，按指定星期几计算
        if (!weekdays.isEmpty()) {
            // 找到下一个重复的星期几
            for (int weekday : weekdays) {
                if (weekday > currentWeekday) {
                    return baseTime.plusDays(weekday - currentWeekday);
                }
            }
            
            // 如果当前星期几已经过了，找下个周期的
            int nextWeekday = weekdays.get(0);
            int daysToAdd = (7 * interval) - currentWeekday + nextWeekday;
            return baseTime.plusDays(daysToAdd);
        } else {
            // 没有指定星期几，按间隔周数计算
            return baseTime.plusWeeks(interval);
        }
    }

    private LocalDateTime calculateNextMonthlyTime(LocalDateTime baseTime) {
        List<Integer> monthDays = parseMonthDays(recurrenceMonthDays);
        int interval = recurrenceInterval != null ? recurrenceInterval : 1;
        return cn.july.orch.meeting.util.RecurrenceRuleUtils.calculateNextMonthlyMultiTime(baseTime, monthDays, interval);
    }

    private LocalDateTime calculateNextYearlyTime(LocalDateTime baseTime) {
        int interval = recurrenceInterval != null ? recurrenceInterval : 1;
        return cn.july.orch.meeting.util.RecurrenceRuleUtils.calculateNextYearlyTime(baseTime, interval);
    }

    private List<Integer> parseWeekdays(String weekdaysStr) {
        return cn.july.orch.meeting.util.RecurrenceRuleUtils.parseWeekdays(weekdaysStr);
    }

    private List<Integer> parseMonthDays(String monthDaysStr) {
        return cn.july.orch.meeting.util.RecurrenceRuleUtils.parseMonthDays(monthDaysStr);
    }

    /**
     * 重复系列是否已取消
     */
    private Integer recurrenceCancelled;

    /**
     * 会前文档列表
     */
    private List<PreMeetingDocumentDTO> preMeetingDocuments;

    /**
     * 实际会议开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际会议结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 实际参会人员open_id列表
     */
    private List<String> actualAttendees;

    /**
     * 计算会议持续时长（分钟）
     */
    public Integer calculateDuration() {
        if (startTime == null || endTime == null) {
            return null;
        }
        return (int) java.time.Duration.between(startTime, endTime).toMinutes();
    }
}