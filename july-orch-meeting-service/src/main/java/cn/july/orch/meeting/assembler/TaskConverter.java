package cn.july.orch.meeting.assembler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.orch.meeting.domain.dto.FileInfoDTO;
import cn.july.orch.meeting.domain.dto.TaskDTO;
import cn.july.orch.meeting.domain.dto.TaskListDTO;
import cn.july.orch.meeting.domain.dto.MeetingBasicInfoDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.domain.entity.TaskInfo;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.service.FileDetailService;
import cn.july.orch.meeting.service.UserInfoQueryService;
import cn.july.orch.meeting.service.NewMeetingQueryService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务组装器
 */
@Slf4j
@Component
public class TaskConverter {

    @Resource
    private UserInfoQueryService userInfoQueryService;
    
    @Resource
    private FileDetailService fileDetailService;
    
    @Resource
    private FileStorageService fileStorageService;
    
    @Resource
    private NewMeetingQueryService newMeetingQueryService;

    /**
     * TaskInfo转TaskDTO
     *
     * @param taskInfo 任务信息
     * @return TaskDTO
     */
    public TaskDTO toTaskDTO(TaskInfo taskInfo) {
        if (taskInfo == null) {
            return null;
        }

        TaskDTO taskDTO = new TaskDTO();
        BeanUtils.copyProperties(taskInfo, taskDTO);

        // 获取负责人头像
        if (StrUtil.isNotBlank(taskInfo.getOwnerOpenId())) {
            String avatarUrl = userInfoQueryService.getUserAvatarUrl(taskInfo.getOwnerOpenId());
            taskDTO.setOwnerAvatarUrl(avatarUrl);
        }
        
        // 处理附件转换
        if (CollUtil.isNotEmpty(taskInfo.getAttachmentsJson())) {
            List<FileInfoDTO> attachmentDTOs = convertAttachmentKeysToFileInfoDTOs(taskInfo.getAttachmentsJson());
            taskDTO.setAttachments(attachmentDTOs);
        } else {
            taskDTO.setAttachments(new ArrayList<>());
        }
        
        // 填充会议基本信息
        if (taskInfo.getMeetingId() != null) {
            MeetingBasicInfoDTO meetingInfo = buildMeetingBasicInfo(taskInfo.getMeetingId(), taskInfo.getMeetingTags());
            taskDTO.setMeetingInfo(meetingInfo);
        }

        return taskDTO;
    }

    /**
     * TaskInfo转TaskListDTO
     *
     * @param taskInfo 任务信息
     * @return TaskListDTO
     */
    public TaskListDTO toTaskListDTO(TaskInfo taskInfo) {
        if (taskInfo == null) {
            return null;
        }

        TaskListDTO taskListDTO = new TaskListDTO();
        BeanUtils.copyProperties(taskInfo, taskListDTO);

        // 设置是否超期
        taskListDTO.setIsOverdue(isOverdue(taskInfo));

        // 获取负责人头像
        if (StrUtil.isNotBlank(taskInfo.getOwnerOpenId())) {
            String avatarUrl = userInfoQueryService.getUserAvatarUrl(taskInfo.getOwnerOpenId());
            taskListDTO.setOwnerAvatarUrl(avatarUrl);
        }

        return taskListDTO;
    }

    /**
     * TaskInfo列表转TaskDTO列表
     *
     * @param taskInfoList 任务信息列表
     * @return TaskDTO列表
     */
    public List<TaskDTO> toTaskDTOList(List<TaskInfo> taskInfoList) {
        if (CollUtil.isEmpty(taskInfoList)) {
            return Collections.emptyList();
        }

        // 批量获取用户头像
        List<String> ownerOpenIds = taskInfoList.stream()
                .map(TaskInfo::getOwnerOpenId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> avatarUrlMap = userInfoQueryService.getUserAvatarUrls(ownerOpenIds);
        
        // 批量获取附件信息
        Set<String> allAttachmentKeys = taskInfoList.stream()
                .filter(taskInfo -> CollUtil.isNotEmpty(taskInfo.getAttachmentsJson()))
                .flatMap(taskInfo -> taskInfo.getAttachmentsJson().stream())
                .collect(Collectors.toSet());
        
        Map<String, FileInfoDTO> attachmentMap = batchConvertAttachmentKeys(allAttachmentKeys);

        return taskInfoList.stream()
                .map(taskInfo -> {
                    TaskDTO taskDTO = new TaskDTO();
                    BeanUtils.copyProperties(taskInfo, taskDTO);

                    // 设置头像
                    if (StrUtil.isNotBlank(taskInfo.getOwnerOpenId())) {
                        taskDTO.setOwnerAvatarUrl(avatarUrlMap.get(taskInfo.getOwnerOpenId()));
                    }
                    
                    // 设置附件
                    if (CollUtil.isNotEmpty(taskInfo.getAttachmentsJson())) {
                        List<FileInfoDTO> attachments = taskInfo.getAttachmentsJson().stream()
                                .map(attachmentMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        taskDTO.setAttachments(attachments);
                    } else {
                        taskDTO.setAttachments(new ArrayList<>());
                    }

                    return taskDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * TaskInfo列表转TaskListDTO列表
     *
     * @param taskInfoList 任务信息列表
     * @return TaskListDTO列表
     */
    public List<TaskListDTO> toTaskListDTOList(List<TaskInfo> taskInfoList) {
        if (CollUtil.isEmpty(taskInfoList)) {
            return Collections.emptyList();
        }

        // 批量获取用户头像
        List<String> ownerOpenIds = taskInfoList.stream()
                .map(TaskInfo::getOwnerOpenId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> avatarUrlMap = userInfoQueryService.getUserAvatarUrls(ownerOpenIds);

        return taskInfoList.stream()
                .map(taskInfo -> {
                    TaskListDTO taskListDTO = new TaskListDTO();
                    BeanUtils.copyProperties(taskInfo, taskListDTO);

                    // 设置是否超期
                    taskListDTO.setIsOverdue(isOverdue(taskInfo));

                    // 设置头像
                    if (StrUtil.isNotBlank(taskInfo.getOwnerOpenId())) {
                        taskListDTO.setOwnerAvatarUrl(avatarUrlMap.get(taskInfo.getOwnerOpenId()));
                    }

                    return taskListDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * TaskAgg转TaskDTO
     *
     * @param taskAgg 任务聚合
     * @return TaskDTO
     */
    public TaskDTO toTaskDTO(TaskAgg taskAgg) {
        if (taskAgg == null || taskAgg.getInfo() == null) {
            return null;
        }
        return toTaskDTO(taskAgg.getInfo());
    }

    /**
     * TaskAgg转TaskListDTO
     *
     * @param taskAgg 任务聚合
     * @return TaskListDTO
     */
    public TaskListDTO toTaskListDTO(TaskAgg taskAgg) {
        if (taskAgg == null || taskAgg.getInfo() == null) {
            return null;
        }
        return toTaskListDTO(taskAgg.getInfo());
    }

    /**
     * TaskAgg列表转TaskDTO列表
     *
     * @param taskAggList 任务聚合列表
     * @return TaskDTO列表
     */
    public List<TaskDTO> toTaskDTOListFromAgg(List<TaskAgg> taskAggList) {
        if (CollUtil.isEmpty(taskAggList)) {
            return Collections.emptyList();
        }

        List<TaskInfo> taskInfoList = taskAggList.stream()
                .map(TaskAgg::getInfo)
                .filter(info -> info != null)
                .collect(Collectors.toList());

        return toTaskDTOList(taskInfoList);
    }

    /**
     * TaskAgg列表转TaskListDTO列表
     *
     * @param taskAggList 任务聚合列表
     * @return TaskListDTO列表
     */
    public List<TaskListDTO> toTaskListDTOListFromAgg(List<TaskAgg> taskAggList) {
        if (CollUtil.isEmpty(taskAggList)) {
            return Collections.emptyList();
        }

        List<TaskInfo> taskInfoList = taskAggList.stream()
                .map(TaskAgg::getInfo)
                .filter(info -> info != null)
                .collect(Collectors.toList());

        return toTaskListDTOList(taskInfoList);
    }

    /**
     * 判断任务是否超期
     *
     * @param taskInfo 任务信息
     * @return 是否超期
     */
    private Boolean isOverdue(TaskInfo taskInfo) {
        if (taskInfo.getDueDate() == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(taskInfo.getDueDate()) &&
               taskInfo.getStatus() != TaskStatusEnum.COMPLETED;
    }
    
    /**
     * 将附件key列表转换为FileInfoDTO列表
     *
     * @param attachmentKeys 附件key列表
     * @return FileInfoDTO列表
     */
    private List<FileInfoDTO> convertAttachmentKeysToFileInfoDTOs(List<String> attachmentKeys) {
        if (CollUtil.isEmpty(attachmentKeys)) {
            return new ArrayList<>();
        }
        
        List<FileInfoDTO> fileInfoDTOs = new ArrayList<>();
        
        for (String key : attachmentKeys) {
            try {
                FileDetailPO fileDetailPO = fileDetailService.getById(key);
                if (fileDetailPO != null) {
                    // 获取文件信息并生成临时链接
                    FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
                    String presignedUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetMinute(new Date(), 60));
                    
                    FileInfoDTO fileInfoDTO = FileInfoDTO.builder()
                            .fileKey(key)
                            .url(presignedUrl)
                            .filename(fileDetailPO.getFilename())
                            .originalFilename(fileDetailPO.getOriginalFilename())
                            .build();
                    
                    fileInfoDTOs.add(fileInfoDTO);
                } else {
                    log.warn("附件不存在，key: {}", key);
                }
            } catch (Exception e) {
                log.error("获取附件信息失败，key: {}", key, e);
            }
        }
        
        return fileInfoDTOs;
    }
    
    /**
     * 批量将附件key转换为FileInfoDTO映射
     *
     * @param attachmentKeys 附件key集合
     * @return 附件key到FileInfoDTO的映射
     */
    private Map<String, FileInfoDTO> batchConvertAttachmentKeys(Set<String> attachmentKeys) {
        if (CollUtil.isEmpty(attachmentKeys)) {
            return new HashMap<>();
        }
        
        Map<String, FileInfoDTO> attachmentMap = new HashMap<>();
        
        for (String key : attachmentKeys) {
            try {
                FileDetailPO fileDetailPO = fileDetailService.getById(key);
                if (fileDetailPO != null) {
                    // 获取文件信息并生成临时链接
                    FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
                    String presignedUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetMinute(new Date(), 60));
                    
                    FileInfoDTO fileInfoDTO = FileInfoDTO.builder()
                            .fileKey(key)
                            .url(presignedUrl)
                            .filename(fileDetailPO.getFilename())
                            .originalFilename(fileDetailPO.getOriginalFilename())
                            .build();
                    
                    attachmentMap.put(key, fileInfoDTO);
                } else {
                    log.warn("附件不存在，key: {}", key);
                }
            } catch (Exception e) {
                log.error("获取附件信息失败，key: {}", key, e);
            }
        }
        
        return attachmentMap;
    }
    
    /**
     * 构建会议基本信息
     *
     * @param meetingId 会议ID
     * @param meetingTags 会议标签列表
     * @return 会议基本信息
     */
    private MeetingBasicInfoDTO buildMeetingBasicInfo(Long meetingId, List<SimpleMeetingTagDTO> meetingTags) {
        try {
            NewMeetingDTO meeting = newMeetingQueryService.getById(meetingId);
            if (meeting == null) {
                return null;
            }
            
            MeetingBasicInfoDTO basicInfo = new MeetingBasicInfoDTO();
            basicInfo.setId(meeting.getId());
            basicInfo.setMeetingName(meeting.getMeetingName());
            basicInfo.setMeetingDescription(meeting.getMeetingDescription());
            basicInfo.setStartTime(meeting.getStartTime());
            basicInfo.setEndTime(meeting.getEndTime());
            basicInfo.setStatus(meeting.getStatus());
            basicInfo.setPriorityLevel(meeting.getPriorityLevel());
            basicInfo.setMeetingLocation(meeting.getMeetingLocation());
            basicInfo.setMeetingUrl(meeting.getMeetingUrl());
            basicInfo.setMinuteUrl(meeting.getMinuteUrl());
            
            // 主持人信息
            if (meeting.getHostUserDetail() != null) {
                basicInfo.setHostUserName(meeting.getHostUserDetail().getName());
            }
            
            // 参会人员信息
            if (CollUtil.isNotEmpty(meeting.getAttendeeDetails())) {
                basicInfo.setAttendeeDetails(meeting.getAttendeeDetails());
                basicInfo.setAttendeeCount(meeting.getAttendeeDetails().size());
            }
            
            // 会议标签信息（使用任务中存储的标签信息）
            if (CollUtil.isNotEmpty(meetingTags)) {
                basicInfo.setMeetingTags(meetingTags);
            }
            
            return basicInfo;
        } catch (Exception e) {
            log.error("获取会议基本信息失败，会议ID：{}", meetingId, e);
            return null;
        }
    }
}
