package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.domain.param.TaskCreateParam;
import cn.july.orch.meeting.domain.param.TaskUpdateParam;
import cn.july.orch.meeting.assembler.MeetingTagAssembler;
import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.domain.po.MeetingTagPO;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.mapper.MeetingTagMapper;
import cn.july.orch.meeting.repository.ITaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务领域服务
 */
@Slf4j
@Service
public class TaskDomainService {

    @Resource
    private ITaskRepository taskRepository;
    
    @Resource
    private NewMeetingQueryService newMeetingQueryService;
    
    @Resource
    private MeetingStandardTagPivotService meetingStandardTagPivotService;
    
    @Resource
    private MeetingTagMapper meetingTagMapper;
    
    @Resource
    private MeetingTagAssembler meetingTagAssembler;

    /**
     * 创建任务（支持任务清单、父任务和附件）
     *
     * @param param 任务创建参数
     * @return 任务ID
     */
    public Long create(TaskCreateParam param) {
        // 获取会议标签
        List<SimpleMeetingTagDTO> meetingTags = getMeetingTags(param.getMeetingId());
        
        TaskAgg taskAgg = TaskAgg.create(param.getTitle(), param.getDescription(), param.getOwnerOpenId(),
                                        param.getOwnerName(), param.getPriority(), param.getDueDate(), param.getMeetingId(),
                                        param.getTaskListId(), param.getParentId(), param.getAttachmentKeys(), meetingTags);
        return taskRepository.insert(taskAgg);
    }

    /**
     * 更新任务
     *
     * @param param 任务更新参数
     */
    public void update(TaskUpdateParam param) {
        TaskAgg taskAgg = taskRepository.findById(param.getTaskId());
        if (taskAgg != null) {
            taskAgg.update(param.getTitle(), param.getDescription(), param.getOwnerOpenId(), 
                          param.getOwnerName(), param.getPriority(), param.getDueDate(), param.getMeetingId());
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 更新任务聚合
     *
     * @param taskAgg 任务聚合
     */
    public void update(TaskAgg taskAgg) {
        if (taskAgg != null) {
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     */
    public void complete(Long taskId) {
        TaskAgg taskAgg = taskRepository.findById(taskId);
        if (taskAgg != null) {
            taskAgg.complete();
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 开始任务
     *
     * @param taskId 任务ID
     */
    public void start(Long taskId) {
        TaskAgg taskAgg = taskRepository.findById(taskId);
        if (taskAgg != null) {
            taskAgg.start();
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     */
    public void delete(Long taskId) {
        TaskAgg taskAgg = taskRepository.findById(taskId);
        if (taskAgg != null) {
            taskAgg.delete();
            taskRepository.delete(taskAgg);
        }
    }

    /**
     * 根据ID查询任务
     *
     * @param taskId 任务ID
     * @return 任务聚合
     */
    public TaskAgg findById(Long taskId) {
        return taskRepository.findById(taskId);
    }

    /**
     * 根据会议ID查询任务列表
     *
     * @param meetingId 会议ID
     * @return 任务聚合列表
     */
    public List<TaskAgg> findByMeetingId(Long meetingId) {
        return taskRepository.findByMeetingId(meetingId);
    }

    /**
     * 根据状态查询任务列表
     *
     * @param status 任务状态
     * @return 任务聚合列表
     */
    public List<TaskAgg> findByStatus(TaskStatusEnum status) {
        return taskRepository.findByStatus(status);
    }

    /**
     * 根据负责人查询任务列表
     *
     * @param ownerOpenId 负责人OpenID
     * @return 任务聚合列表
     */
    public List<TaskAgg> findByOwner(String ownerOpenId) {
        return taskRepository.findByOwner(ownerOpenId);
    }

    /**
     * 处理超期任务
     */
    public void processOverdueTasks() {
        List<TaskAgg> overdueTasks = taskRepository.findOverdueTasks();
        for (TaskAgg taskAgg : overdueTasks) {
            if (taskAgg.isOverdue()) {
                taskAgg.markOverdue();
                taskRepository.update(taskAgg);
            }
        }
    }

    /**
     * 更新飞书任务ID
     *
     * @param taskId 任务ID
     * @param feishuTaskId 飞书任务ID
     */
    public void updateFeishuTaskId(Long taskId, String feishuTaskId) {
        TaskAgg taskAgg = taskRepository.findById(taskId);
        if (taskAgg != null) {
            taskAgg.updateFeishuTaskId(feishuTaskId);
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 根据飞书任务ID完成任务
     *
     * @param feishuTaskId 飞书任务ID
     */
    public void completeByFeishuTaskId(String feishuTaskId) {
        TaskAgg taskAgg = taskRepository.findByFeishuTaskId(feishuTaskId);
        if (taskAgg != null) {
            taskAgg.complete();
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 根据飞书任务ID取消完成任务（重新开始）
     *
     * @param feishuTaskId 飞书任务ID
     */
    public void uncompleteByFeishuTaskId(String feishuTaskId) {
        TaskAgg taskAgg = taskRepository.findByFeishuTaskId(feishuTaskId);
        if (taskAgg != null) {
            taskAgg.start(); // 将状态改为进行中
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 根据飞书任务ID删除任务
     *
     * @param feishuTaskId 飞书任务ID
     */
    public void deleteByFeishuTaskId(String feishuTaskId) {
        TaskAgg taskAgg = taskRepository.findByFeishuTaskId(feishuTaskId);
        if (taskAgg != null) {
            taskAgg.delete();
            taskRepository.delete(taskAgg);
        }
    }

    /**
     * 根据飞书任务ID查询任务
     *
     * @param feishuTaskId 飞书任务ID
     * @return 任务聚合
     */
    public TaskAgg findByFeishuTaskId(String feishuTaskId) {
        return taskRepository.findByFeishuTaskId(feishuTaskId);
    }

    /**
     * 获取会议标签信息（仅包含id、name、color）
     *
     * @param meetingId 会议ID
     * @return 会议标签列表
     */
    private List<SimpleMeetingTagDTO> getMeetingTags(Long meetingId) {
        if (meetingId == null) {
            return new ArrayList<>();
        }
        
        try {
            // 1. 根据会议ID获取会议信息
            NewMeetingDTO meeting = newMeetingQueryService.getById(meetingId);
            if (meeting == null || meeting.getMeetingStandardId() == null) {
                log.debug("会议不存在或未关联会议标准，会议ID：{}", meetingId);
                return new ArrayList<>();
            }
            
            // 2. 根据会议标准ID获取关联的标签ID列表
            List<Long> tagIds = meetingStandardTagPivotService.getTagIdsByStandardId(meeting.getMeetingStandardId());
            if (CollUtil.isEmpty(tagIds)) {
                log.debug("会议标准未关联标签，会议标准ID：{}", meeting.getMeetingStandardId());
                return new ArrayList<>();
            }
            
            // 3. 根据标签ID列表获取标签详细信息
            List<MeetingTagPO> tagPOs = meetingTagMapper.selectBatchIds(tagIds);
            if (CollUtil.isEmpty(tagPOs)) {
                log.debug("标签信息不存在，标签IDs：{}", tagIds);
                return new ArrayList<>();
            }
            
            // 4. 转换为SimpleMeetingTagDTO对象（仅包含id、name、color）
            return tagPOs.stream()
                    .map(tagPO -> {
                        SimpleMeetingTagDTO dto = new SimpleMeetingTagDTO();
                        dto.setId(tagPO.getId());
                        dto.setName(tagPO.getName());
                        dto.setColor(tagPO.getColor());
                        return dto;
                    })
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取会议标签失败，会议ID：{}", meetingId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据飞书任务ID更新任务信息
     *
     * @param feishuTaskId 飞书任务ID
     * @param title 任务标题
     * @param description 任务描述
     * @param ownerOpenId 负责人OpenID
     * @param ownerName 负责人名称
     * @param priority 优先级
     * @param dueDate 截止时间
     */
    public void updateByFeishuTaskId(String feishuTaskId, String title, String description,
                                   String ownerOpenId, String ownerName,
                                   TaskPriorityEnum priority, LocalDateTime dueDate) {
        TaskAgg taskAgg = taskRepository.findByFeishuTaskId(feishuTaskId);
        if (taskAgg != null) {
            taskAgg.update(title, description, ownerOpenId, ownerName, priority, dueDate, taskAgg.getInfo().getMeetingId());
            taskRepository.update(taskAgg);
        }
    }
}