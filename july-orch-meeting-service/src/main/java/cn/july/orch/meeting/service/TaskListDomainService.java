package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.entity.TaskListAgg;
import cn.july.orch.meeting.repository.ITaskListRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务清单领域服务
 */
@Slf4j
@Service
public class TaskListDomainService {

    @Resource
    private ITaskListRepository taskListRepository;

    /**
     * 创建任务清单
     *
     * @param name 清单名称
     * @param description 清单描述
     * @param parentId 父清单ID
     * @return 任务清单ID
     */
    public Long create(String name, String description, Long parentId) {
        TaskListAgg taskListAgg = TaskListAgg.create(name, description, parentId);
        return taskListRepository.insert(taskListAgg);
    }

    /**
     * 更新任务清单
     *
     * @param id 任务清单ID
     * @param name 清单名称
     * @param description 清单描述
     * @param parentId 父清单ID
     */
    public void update(Long id, String name, String description, Long parentId) {
        TaskListAgg taskListAgg = taskListRepository.findById(id);
        if (taskListAgg != null) {
            taskListAgg.update(name, description, parentId);
            taskListRepository.update(taskListAgg);
        }
    }

    /**
     * 删除任务清单
     *
     * @param id 任务清单ID
     */
    public void delete(Long id) {
        TaskListAgg taskListAgg = taskListRepository.findById(id);
        if (taskListAgg != null) {
            taskListAgg.delete();
            taskListRepository.delete(taskListAgg);
        }
    }

    /**
     * 根据ID查询任务清单
     *
     * @param id 任务清单ID
     * @return 任务清单聚合
     */
    public TaskListAgg findById(Long id) {
        return taskListRepository.findById(id);
    }

    /**
     * 根据名称查询任务清单
     *
     * @param name 清单名称
     * @return 任务清单列表
     */
    public List<TaskListAgg> findByName(String name) {
        return taskListRepository.findByName(name);
    }

    /**
     * 根据创建人查询任务清单
     *
     * @param createUserId 创建人ID
     * @return 任务清单列表
     */
    public List<TaskListAgg> findByCreateUserId(String createUserId) {
        return taskListRepository.findByCreateUserId(createUserId);
    }
}