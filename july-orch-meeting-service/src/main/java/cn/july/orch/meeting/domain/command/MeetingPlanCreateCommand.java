package cn.july.orch.meeting.domain.command;

import cn.july.orch.meeting.domain.dto.RecurrenceRule;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划创建Command
 * @date 2025-01-24
 */
@Data
public class MeetingPlanCreateCommand {

    @ApiModelProperty(value = "会议规划名称", required = true)
    @NotBlank(message = "会议规划名称不能为空")
    private String planName;

    @ApiModelProperty(value = "会议规划描述")
    private String planDescription;

    @ApiModelProperty(value = "计划开始时间", required = true)
    @NotNull(message = "计划开始时间不能为空")
    private LocalDateTime plannedStartTime;

    @ApiModelProperty(value = "计划结束时间", required = true)
    @NotNull(message = "计划结束时间不能为空")
    private LocalDateTime plannedEndTime;

    @ApiModelProperty(value = "会议标准ID", required = true)
    @NotNull(message = "会议标准ID不能为空")
    private Long meetingStandardId;

    @ApiModelProperty(value = "提醒人员列表")
    private List<String> attendees;

    @ApiModelProperty(value = "重复规则")
    private RecurrenceRule recurrenceRule;

    @ApiModelProperty(value = "会前文档文件ID列表")
    private List<String> preMeetingDocuments;
}
