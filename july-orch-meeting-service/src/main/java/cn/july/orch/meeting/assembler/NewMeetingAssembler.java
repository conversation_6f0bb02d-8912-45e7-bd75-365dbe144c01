package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.command.NewMeetingCreateCommand;
import cn.july.orch.meeting.domain.command.NewMeetingUpdateCommand;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingListDTO;
import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 新会议装配器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {NewMeetingCommandConverter.class})
public interface NewMeetingAssembler {

    /**
     * Command转Entity
     * 使用 NewMeetingCommandConverter 处理复杂映射
     */
    @Mapping(target = "meetingTags", source = "meetingTagIds", qualifiedByName = "mapMeetingTagIds")
    @Mapping(target = "preMeetingDocuments", source = "preMeetingDocumentKeys", qualifiedByName = "mapPreMeetingDocumentKeys")
    NewMeeting toEntity(NewMeetingCreateCommand command);

    @Mapping(target = "preMeetingDocuments", source = "preMeetingDocuments", qualifiedByName = "mapPreMeetingDocumentKeys")
    NewMeeting toEntity(NewMeetingUpdateCommand command);

    NewMeeting toEntity(NewMeetingPO dto);

    /**
     * Entity转DTO
     */
    NewMeetingDTO toDTO(NewMeeting entity);

    /**
     * Entity列表转DTO列表
     */
    List<NewMeetingDTO> toDTOList(List<NewMeeting> entityList);

    /**
     * Entity转分页列表DTO
     */
    NewMeetingListDTO toListDTO(NewMeeting entity);

    /**
     * Entity列表转分页列表DTO列表
     */
    List<NewMeetingListDTO> toListDTOList(List<NewMeeting> entityList);

    /**
     * 将字符串转换为整数列表
     */
    @Named("stringToIntegerList")
    default List<Integer> stringToIntegerList(String str) {
        if (str == null || str.isEmpty()) {
            return null;
        }
        return Arrays.stream(str.split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    /**
     * 将整数列表转换为字符串
     */
    @Named("integerListToString")
    default String integerListToString(List<Integer> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }
} 