knife4j:
  enable: true
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://************:6379",
            "password": "July123456.",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
#dromara:
#  x-file-storage: #文件存储配置
#    default-platform: minio-1 #默认使用的存储平台
#    thumbnail-suffix: ".min.jpg"
#    minio:
#      - platform: minio-1 # 存储平台标识
#        enable-storage: true  # 启用存储
#        access-key: FHZArYPFxUOjadtHNGbo
#        secret-key: iSjOzz2RB1ibZ2jV4WpOi2Tolp7kTZNfPDfB18rg
#        end-point: http://coe-file-sit.pengfeijituan.com:9000/
#        bucket-name: meeting-1
#        domain: http://coe-file-sit.pengfeijituan.com:9000/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
#        base-path: ${spring.application.name}/
dromara:
  x-file-storage: #文件存储配置
    default-platform: huawei-obs-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    huawei-obs:
      - platform: huawei-obs-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: ERMZYOVKRBTVCAIBTIJ2
        secret-key: phJPIFwjjdnFnjWCOG5mihaMhLBsMUnlneQED4mE
        end-point: obs.cn-north-4.myhuaweicloud.com
        bucket-name: test-kangjian
        domain: https://test-kangjian.obs.cn-north-4.myhuaweicloud.com:443/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
        base-path: genn-pf-orch-meeting
feishu:
  mini:
#    appId: cli_a80a244ad965d00d
#    appSecret: iHiwU7PYLQeN8oCH8ku4OB7ItEa0Vh8T
    appId: cli_a808965153f95013
    appSecret: xrOQJck37DRjTYvjyN5moR7glft5yaXD
#    appId: cli_a80b7518376f900e
#    appSecret: wpI6VljHISz4evCckGvxseeExT8YhNmO
july:
  meeting:
    cardSend:
      jumpUrl:
        evaluationUrl: http://www.baidu.com

    agent:
      invokeDomain: https://cerebro-sit.genn.cn
      # 智能体应用ID配置
      meetingAnalysisAppId: 6892ae7c9e9b7bc7598f5c54
      aiTranscriptAppId: 68ad718ca364b37b2d3b5a53
      qaAppId: 688c63f47e9027870e39c7dd
      fileSummaryAppId: 688c63597e9027870e39c5d9
      # 智能体鉴权配置
      summaryAuthorization: Bearer gennai-yRjuiUeSsiRQ2IXwke4UrWkpFx9zKBmrgh2yhLaOM2zpvTVNb5w4vqdje
      qaAuthorization: Bearer gennai-iFJdQucHdmkZijiNRhvmjYC6vf1oIALgqPdjFbSE59bTUpsZJ8RhOQN
      reportAuthorization: Bearer gennai-eXd1B6Hty4d70trGu3CuIGGkfSS2CMNYFkyHsbUYwY4WiftvxNoU56sYoh
      aiTranscriptAuthorization: Bearer gennai-uJELy30V6ttN6ZbiDAbClCOEoZh4mGOMrW6XzfkAeaku48l69OxbRO3YXANjkK
    permission:
      excludePatterns:
        - /user/getUserInfo
        - /file/**
        - /app/**
        - /analysis-report/**
        - /test/**
  database:
    multi:
      db:
        july_orch_meeting:
          primary: true
          master:
            jdbcUrl: ***********************************************************************************************************************************************************************************
            username: root
            password: July123456.
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10