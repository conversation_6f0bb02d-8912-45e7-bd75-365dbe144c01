package cn.july.feishu.exception;

import cn.july.core.exception.MessageCodeWrap;

/**
 * <AUTHOR>
 */
public enum FeishuErrorCode implements MessageCodeWrap {

    ROBOT_SEND_ERROR("201", "机器人发送消息失败"),
    ROBOT_RESPONSE_CODE_ERROR("202", "机器人响应状态码异常"),

    TENANT_TOKEN_FAIL("301","获取飞书tenant_token失败"),
    USER_TOKEN_FAIL("302","获取飞书user_access_token失败"),
    REDIRECT_URI_NOT_EXIST("303","飞书登录跳转路径为空"),
    REFRESH_USER_TOKEN_OVERTIME("304","应用需用户重新免登授权!"),
    REFRESH_USER_TOKEN_FAIL("305","刷新飞书user_access_token失败!"),
    TELEPHONE_NOT_EXIST("306","手机号不能为空"),
    GET_OPEN_ID_FAIL("307","获取用户openId失败"),
    USER_INFO_FAIL("308","获取飞书用户信息失败"),
    LOGIN_USER_INFO_FAIL("309","获取登录用户信息失败"),
    SEARCH_USER_INFO_FAIL("310","搜索用户信息失败"),
    GET_JS_SDK_TICKET_FAIL("311","获取JSAPI临时授权凭证失败"),
    TENANT_INFO_FAIL("312","获取企业信息失败"),
    CONTACT_INFO_FAIL("313","获取企业通讯录范围失败"),
    DEPARTMENT_INFO_FAIL("314","获取部门信息失败"),
    CHILDREN_DEPARTMENT_INFO_FAIL("315","获取子部门信息失败"),
    PARENT_DEPARTMENT_INFO_FAIL("316","获取父部门信息失败"),
    OPEN_ID_NOT_EXIST("317","获取openId失败"),

    CALENDAR_ID_NOT_EXIST("401","日历Id不能为空"),
    CALENDAR_INFO_ERROR("401","获取主日历异常"),
    SUMMARY_NOT_EXIST("402","日程标题不能为空"),
    ORGANIZER_ID_NOT_EXIST("403","日程组织者Id不能为空"),
    START_END_TIME_NOT_EXIST("404","日程起止时间不能为空"),
    CREATE_CALENDAR_EVENT_FAIL("405","创建日程失败"),
    EVENT_ID_NOT_EXIST("406","日程Id不能为空"),
    DELETE_CALENDAR_EVENT_FAIL("407","删除日程失败"),
    UPDATE_CALENDAR_EVENT_FAIL("408","更新日程失败"),
    GET_CALENDAR_EVENT_FAIL("409","获取日程失败"),
    LIST_GET_CALENDAR_EVENT_FAIL("410","批量获取日程失败"),
    GET_CALENDAR_EVENT_INSTANCES_FAIL("411","获取重复日程实例失败"),
    GET_CALENDAR_EVENT_ATTENDEES_FAIL("412","获取日程参与人列表失败"),
    CREATE_CALENDAR_EVENT_ATTENDEES_NOT_EXIST("413","添加日程参与人不能为空"),
    CREATE_CALENDAR_EVENT_ATTENDEES_FAIL("414","创建日程参与人失败"),
    DELETE_CALENDAR_EVENT_ATTENDEES_FAIL("415","删除日程参与人失败"),
    SUBSCRIPTION_CALENDAR_EVENT_FAIL("416","订阅日程变更事件失败"),
    UN_SUBSCRIPTION_CALENDAR_EVENT_FAIL("417","取消订阅日程变更事件失败"),
    CALENDAR_AUTHORIZE_FAIL("418","日历授权失败"),


    CREATE_APPROVAL_INSTANCE_FAIL("501","创建飞书审核实例失败"),
    APPROVAL_CODE_NOT_EXIST("502","审核定义Code不能为空"),
    FORM_NOT_EXIST("503","审核表单不能为空"),
    GET_APPROVAL_FAIL("504","获取审核定义失败"),
    APPROVAL_FORM_NOT_EXIST("505","审核定义表单数据为空"),
    APPROVAL_FORM_CHECK_ERROR("506","审核定义表单数据校验失败"),
    APPROVAL_UPLOAD_FAIL("507","飞书审核上传文件失败"),
    APPROVAL_INSTANCE_INFO_FAIL("508","获取审批实例详情失败"),
    APPROVAL_SUBSCRIBE_EVENT_FAIL("509","审批定义事件订阅失败"),
    DEFAULT_OPEN_ID_NOT_EXIST("510","审批默认发起人配置为空"),

    // 600-699 会议相关
    MEETING_GET_FAIL("601","获取会议信息失败"),
    MEETING_SET_PERMISSION_FAIL("602","设置会议录制权限失败"),
    MINUTE_GET_FAIL("603","获取妙计转文字失败"),
    MEETING_ROOM_FAIL("604","获取会议室失败"),

    // 700-799 应用机器人相关
    SEND_MESSAGE_FAIL("701","发送应用消息失败"),
    UPDATE_MESSAGE_FAIL("702","更新应用消息失败"),







    ;

    private final String code;
    private final String description;

    FeishuErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }


    @Override
    public String getBizCode() {
        return "41";
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
