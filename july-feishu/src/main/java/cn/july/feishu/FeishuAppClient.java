package cn.july.feishu;


import cn.july.feishu.config.AppConfig;
import cn.july.feishu.properties.FeishuProperties;
import cn.july.feishu.service.*;
import com.lark.oapi.Client;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;

@Getter
@Slf4j
public class FeishuAppClient {

    private AuthService authService;
    private ContactService contactService;
    private CalendarService calendarService;
    private RobotService robotService;
    private TenantService tenantService;
    private ApprovalService approvalService;
    private CalendarEventService calendarEventService;
    private MeetService meetService;
    private RoomService roomService;

    @Resource
    private Client feishuClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public static final class Builder {
        private final AppConfig appConfig = new AppConfig();

        public Builder(FeishuProperties properties, Client feishuClient, StringRedisTemplate redisTemplate) {
            appConfig.setAppId(properties.getAppId());
            appConfig.setAppSecret(properties.getAppSecret());
            appConfig.setFeishuClient(feishuClient);
            appConfig.setRedisTemplate(redisTemplate);
        }
        public FeishuAppClient build() {
            FeishuAppClient client = new FeishuAppClient();
            client.authService = new AuthService(appConfig);
            client.contactService = new ContactService(appConfig);
            client.calendarService = new CalendarService(appConfig);
            client.robotService = new RobotService(appConfig);
            client.tenantService = new TenantService(appConfig);
            client.approvalService = new ApprovalService(appConfig,client.authService,client.contactService);
            client.calendarEventService = new CalendarEventService(appConfig);
            client.meetService = new MeetService(appConfig);
            client.roomService = new RoomService(appConfig);
            return client;
        }
    }
}
