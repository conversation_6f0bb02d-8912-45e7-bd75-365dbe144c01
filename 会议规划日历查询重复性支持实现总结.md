# 会议规划日历查询重复性支持实现总结

## 实现概述

本次实现为会议规划功能的日历视图查询接口添加了重复性会议支持，能够正确处理和展示重复性会议在指定时间范围内的所有重复实例。

## 核心功能

### 1. 查询策略优化

采用双重查询策略：
- **一次性会议查询**：查询原始开始时间在查询范围内的会议
- **重复性会议查询**：查询可能产生重复实例的会议规划

### 2. 重复性会议展开

- 在内存中展开重复性会议的重复实例
- 支持日重复、周重复、月重复、年重复
- 智能处理重复结束日期和特殊日期（如2月29日）

## 详细实现

### 1. DTO扩展

**文件**: `MeetingPlanCalendarDTO.java`

新增字段：
```java
@ApiModelProperty(value = "是否重复会议")
private IsRecurringEnum isRecurring;

@ApiModelProperty(value = "重复类型")
private RecurrenceTypeEnum recurrenceType;

@ApiModelProperty(value = "重复间隔")
private Integer recurrenceInterval;

@ApiModelProperty(value = "每周重复的星期几")
private String recurrenceWeekdays;

@ApiModelProperty(value = "每月重复的日期列表")
private String recurrenceMonthDays;

@ApiModelProperty(value = "重复结束日期")
private LocalDate recurrenceEndDate;

@ApiModelProperty(value = "是否为重复实例")
private Boolean isRecurringInstance;

@ApiModelProperty(value = "原始会议规划ID")
private Long originalPlanId;
```

### 2. 数据库查询优化

**文件**: `MeetingPlanMapper.xml`

#### 一次性会议查询（原有查询增强）
```xml
<!-- 查询日历维度的会议规划 -->
<select id="queryCalendar" resultType="cn.july.orch.meeting.domain.dto.MeetingPlanCalendarDTO">
    SELECT
        mp.id,
        mp.plan_name AS planName,
        -- ... 其他字段
        mp.is_recurring AS isRecurring,
        mp.recurrence_type AS recurrenceType,
        mp.recurrence_interval AS recurrenceInterval,
        mp.recurrence_weekdays AS recurrenceWeekdays,
        mp.recurrence_month_days AS recurrenceMonthDays,
        mp.recurrence_end_date AS recurrenceEndDate
    FROM meeting_plan mp
    LEFT JOIN meeting_standard ms ON mp.meeting_standard_id = ms.id
    WHERE mp.planned_start_time >= #{query.startDate}
    AND mp.planned_start_time < DATE_ADD(#{query.endDate}, INTERVAL 1 DAY)
    -- ... 其他条件
</select>
```

#### 重复性会议查询（新增）
```xml
<!-- 查询重复性会议规划 -->
<select id="queryRecurringPlans" resultType="cn.july.orch.meeting.domain.dto.MeetingPlanCalendarDTO">
    SELECT
        mp.id,
        mp.plan_name AS planName,
        -- ... 其他字段
        mp.is_recurring AS isRecurring,
        mp.recurrence_type AS recurrenceType,
        mp.recurrence_interval AS recurrenceInterval,
        mp.recurrence_weekdays AS recurrenceWeekdays,
        mp.recurrence_month_days AS recurrenceMonthDays,
        mp.recurrence_end_date AS recurrenceEndDate
    FROM meeting_plan mp
    LEFT JOIN meeting_standard ms ON mp.meeting_standard_id = ms.id
    WHERE mp.is_recurring = 1
    AND mp.planned_start_time <= #{query.endDate}
    AND (mp.recurrence_end_date IS NULL OR mp.recurrence_end_date >= #{query.startDate})
    -- ... 其他条件
</select>
```

### 3. 服务层逻辑实现

**文件**: `MeetingPlanService.java`

#### 主要查询方法
```java
public List<MeetingPlanCalendarDTO> queryCalendar(MeetingPlanCalendarQuery query) {
    // 1. 查询一次性会议（原始开始时间在查询范围内）
    List<MeetingPlanCalendarDTO> oneTimePlans = meetingPlanMapper.queryCalendar(query);
    
    // 2. 查询重复性会议（可能产生重复实例的会议）
    List<MeetingPlanCalendarDTO> recurringPlans = meetingPlanMapper.queryRecurringPlans(query);
    
    // 3. 展开重复性会议的重复实例
    List<MeetingPlanCalendarDTO> expandedPlans = expandRecurringPlans(recurringPlans, query);
    
    // 4. 合并排序
    List<MeetingPlanCalendarDTO> allPlans = new ArrayList<>();
    allPlans.addAll(oneTimePlans);
    allPlans.addAll(expandedPlans);
    
    return sortPlans(allPlans);
}
```

#### 重复实例生成逻辑
```java
private List<MeetingPlanCalendarDTO> generateRecurringInstances(MeetingPlanCalendarDTO plan, MeetingPlanCalendarQuery query) {
    List<MeetingPlanCalendarDTO> instances = new ArrayList<>();
    LocalDateTime currentTime = plan.getPlannedStartTime();
    LocalDateTime queryStart = query.getStartDate().atStartOfDay();
    LocalDateTime queryEnd = query.getEndDate().atStartOfDay().plusDays(1);
    
    // 限制最大重复次数，防止无限循环
    int maxIterations = 1000;
    int iterationCount = 0;
    
    while (currentTime.isBefore(queryEnd) && iterationCount < maxIterations) {
        // 检查是否在查询范围内
        if (isInQueryRange(currentTime, queryStart, queryEnd)) {
            MeetingPlanCalendarDTO instance = createRecurringInstance(plan, currentTime);
            instances.add(instance);
        }
        
        // 计算下次重复时间
        LocalDateTime nextTime = calculateNextRecurrenceTime(plan, currentTime);
        if (nextTime == null || nextTime.equals(currentTime)) {
            break; // 无法计算下次时间或时间没有变化，退出循环
        }
        
        currentTime = nextTime;
        iterationCount++;
        
        // 检查重复结束条件
        if (shouldStopRecurrence(plan, currentTime)) {
            break;
        }
    }
    
    return instances;
}
```

### 4. 重复时间计算

支持所有重复类型的计算：

#### 日重复
```java
case DAILY:
    return currentTime.plusDays(interval);
```

#### 周重复
```java
case WEEKLY:
    if (StringUtils.hasText(plan.getRecurrenceWeekdays())) {
        return calculateNextWeeklyTime(plan, currentTime);
    } else {
        return currentTime.plusWeeks(interval);
    }
```

#### 月重复
```java
case MONTHLY:
    if (StringUtils.hasText(plan.getRecurrenceMonthDays())) {
        return calculateNextMonthlyTime(plan, currentTime);
    } else {
        return currentTime.plusMonths(interval);
    }
```

#### 年重复
```java
case YEARLY:
    return calculateNextYearlyTime(plan, currentTime);
```

### 5. 特殊日期处理

#### 2月29日处理
```java
private LocalDateTime calculateNextYearlyTime(MeetingPlanCalendarDTO plan, LocalDateTime currentTime) {
    int interval = plan.getRecurrenceInterval() != null ? plan.getRecurrenceInterval() : 1;
    LocalDateTime nextYear = currentTime.plusYears(interval);
    
    // 处理2月29日等特殊日期
    LocalDate originalDate = currentTime.toLocalDate();
    LocalDate nextYearDate = nextYear.toLocalDate();
    
    if (originalDate.getMonthValue() == 2 && originalDate.getDayOfMonth() == 29) {
        if (!nextYearDate.isLeapYear()) {
            nextYear = nextYear.withDayOfMonth(28);
        }
    }
    
    return nextYear;
}
```

#### 月重复多选处理
```java
private LocalDateTime calculateNextMonthlyTime(MeetingPlanCalendarDTO plan, LocalDateTime currentTime) {
    List<Integer> monthDays = parseMonthDays(plan.getRecurrenceMonthDays());
    int interval = plan.getRecurrenceInterval() != null ? plan.getRecurrenceInterval() : 1;
    
    if (monthDays.isEmpty()) {
        return currentTime.plusMonths(interval);
    }
    
    int currentDay = currentTime.getDayOfMonth();
    
    // 找到下一个重复的日期
    for (int monthDay : monthDays) {
        if (monthDay > currentDay) {
            LocalDateTime nextTime = currentTime.withDayOfMonth(monthDay);
            if (nextTime.isAfter(currentTime)) {
                return nextTime;
            }
        }
    }
    
    // 当月没有合适的日期，找下个月
    LocalDateTime nextMonth = currentTime.plusMonths(interval);
    int nextMonthDay = monthDays.get(0);
    int maxDay = nextMonth.toLocalDate().lengthOfMonth();
    int actualDay = Math.min(nextMonthDay, maxDay);
    
    return nextMonth.withDayOfMonth(actualDay);
}
```

## 性能优化

### 1. 查询优化
- 使用精确的SQL条件过滤重复性会议
- 避免查询不相关的会议规划

### 2. 内存优化
- 限制最大重复次数（1000次）防止无限循环
- 及时检查重复结束条件

### 3. 算法优化
- 智能计算下次重复时间
- 避免重复计算相同的时间点

## 测试验证

**文件**: `MeetingPlanCalendarQueryTest.java`

包含完整的测试用例：
- 一次性会议查询测试
- 各种重复类型的会议测试
- 重复实例生成测试
- 边界条件测试

## 使用示例

### 查询2024年1月的所有会议
```java
MeetingPlanCalendarQuery query = new MeetingPlanCalendarQuery();
query.setStartDate(LocalDate.of(2024, 1, 1));
query.setEndDate(LocalDate.of(2024, 1, 31));

List<MeetingPlanCalendarDTO> calendarPlans = meetingPlanService.queryCalendar(query);
```

### 返回结果示例
```json
[
  {
    "id": 1,
    "planName": "一次性会议",
    "plannedStartTime": "2024-01-15T10:00:00",
    "isRecurring": "NO",
    "isRecurringInstance": false
  },
  {
    "id": 2,
    "planName": "每日站会",
    "plannedStartTime": "2024-01-01T09:00:00",
    "isRecurring": "YES",
    "isRecurringInstance": false,
    "recurrenceType": "DAILY"
  },
  {
    "id": 2,
    "planName": "每日站会",
    "plannedStartTime": "2024-01-02T09:00:00",
    "isRecurring": "YES",
    "isRecurringInstance": true,
    "originalPlanId": 2,
    "recurrenceType": "DAILY"
  }
]
```

## 总结

本次实现成功为会议规划日历查询功能添加了完整的重复性会议支持：

1. **功能完整**：支持所有重复类型（日、周、月、年）
2. **性能优化**：使用双重查询策略，避免不必要的数据处理
3. **逻辑清晰**：代码结构清晰，易于维护和扩展
4. **测试完备**：包含完整的测试用例验证功能正确性
5. **向后兼容**：不影响现有的一次性会议查询功能

该实现能够满足日历视图展示重复性会议的所有需求，为用户提供完整的会议规划视图。
