-- 重复性会议规划功能优化
-- 1. 支持年重复
-- 2. 按月重复日期支持多选

-- 更新 meeting_plan 表
ALTER TABLE `meeting_plan` 
CHANGE COLUMN `recurrence_month_day` `recurrence_month_days` varchar(100) COMMENT '每月重复的日期列表(1,15,30)';

-- 更新 new_meeting 表  
ALTER TABLE `new_meeting`
CHANGE COLUMN `recurrence_month_day` `recurrence_month_days` varchar(100) COMMENT '每月重复的日期列表(1,15,30)';

-- 更新重复类型枚举注释
ALTER TABLE `meeting_plan` 
MODIFY COLUMN `recurrence_type` varchar(20) COMMENT '重复类型(DAILY/WEEKLY/MONTHLY/YEARLY)';

ALTER TABLE `new_meeting`
MODIFY COLUMN `recurrence_type` varchar(20) COMMENT '重复类型(DAILY/WEEKLY/MONTHLY/YEARLY)';
